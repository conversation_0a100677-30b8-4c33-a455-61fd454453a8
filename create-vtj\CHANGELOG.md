# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [0.12.8](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.12.8) (2025-07-31)


### Bug Fixes

* 🐛 project tempalte ([bdefc48](https://gitee.com/newgateway/vtj/commits/bdefc484bf23a8828693f327f6049d1b19fe5c7c))





## [0.12.7](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.12.7) (2025-07-26)


### Bug Fixes

* 🐛 create-vtj template ([e8b1d0c](https://gitee.com/newgateway/vtj/commits/e8b1d0cad505d01db32cc7fc84bc782595cd48b1))





## [0.12.6](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.12.6) (2025-07-23)


### Features

* ✨ 支持自定义模型 ([37ae362](https://gitee.com/newgateway/vtj/commits/37ae362786659be5bb343290ad0fbd02dbf877f1))





## [0.12.5](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.12.5) (2025-07-16)

**Note:** Version bump only for package create-vtj





## [0.12.4](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.12.4) (2025-07-08)


### Bug Fixes

* 🐛 升级依赖 ([dbee35b](https://gitee.com/newgateway/vtj/commits/dbee35bd867a44f8c71c117fa90d56d108144a6b))





## [0.12.3](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.12.3) (2025-06-30)

**Note:** Version bump only for package create-vtj





## [0.12.2](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.12.2) (2025-06-16)

**Note:** Version bump only for package create-vtj





## [0.12.1](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.12.1) (2025-06-11)

**Note:** Version bump only for package create-vtj





# [0.12.0](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.12.0) (2025-04-22)

**Note:** Version bump only for package create-vtj





# [0.12.0-alpha.1](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.12.0-alpha.1) (2025-04-22)

**Note:** Version bump only for package create-vtj





# [0.12.0-alpha.0](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.12.0-alpha.0) (2025-04-21)

**Note:** Version bump only for package create-vtj





## [0.11.3](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.11.3) (2025-04-09)


### Bug Fixes

* 🐛 物料项目工程模版缺少默认配置 ([905a137](https://gitee.com/newgateway/vtj/commits/905a13798821754f72d9fd23fc59ef1f5aefbc50))





## [0.11.2](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.11.2) (2025-03-20)

**Note:** Version bump only for package create-vtj





## [0.11.1](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.11.1) (2025-03-18)

**Note:** Version bump only for package create-vtj





## [0.11.1-alpha.1](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.11.1-alpha.1) (2025-03-18)

**Note:** Version bump only for package create-vtj





# [0.11.0](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.11.0) (2025-03-17)


### Features

* ✨ 设计器扩展开发项目工程模版 ([1565b46](https://gitee.com/newgateway/vtj/commits/1565b46d82c9aab71daa1bf06b3a194cc028921b))





## [0.10.8](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.8) (2025-03-14)

**Note:** Version bump only for package create-vtj





## [0.10.7](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.7) (2025-03-14)


### Bug Fixes

* 🐛 normalizedStyle ([b649f10](https://gitee.com/newgateway/vtj/commits/b649f109af7b11f29fcc51b523185c3302706d9a))





## [0.10.6](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.6) (2025-03-13)


### Features

* ✨ 物料开发工程模版 ([b1e88e8](https://gitee.com/newgateway/vtj/commits/b1e88e852df84586600a5a0ef00a89bed55bc8b6))





## [0.10.5](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.5) (2025-03-11)


### Bug Fixes

* 🐛 更新uniapp依赖, 优化renderer ([463b1f7](https://gitee.com/newgateway/vtj/commits/463b1f7419b5373c514eaaf285a989c5220f7934))


### Features

* ✨ uniapp 支持微信小程序 ([ca67024](https://gitee.com/newgateway/vtj/commits/ca6702490867df9badb5f045aee76c2df14f2052))





## [0.10.4](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.4) (2025-03-04)

**Note:** Version bump only for package create-vtj





## [0.10.3](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.3) (2025-02-26)


### Bug Fixes

* 🐛 project gitignore ([2794a00](https://gitee.com/newgateway/vtj/commits/2794a00ca5254f4e08028dba1bf094b003585a3d))





## [0.10.2](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.2) (2025-02-26)


### Bug Fixes

* 🐛 调整uniapp本地文件存储目录 ([fcee7aa](https://gitee.com/newgateway/vtj/commits/fcee7aa537d68dfe9f9a0a03f9516c64810462b7))





## [0.10.1](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.1) (2025-02-25)

**Note:** Version bump only for package create-vtj





## [0.10.1-alpha.3](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.1-alpha.3) (2025-02-25)

**Note:** Version bump only for package create-vtj





## [0.10.1-alpha.2](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.1-alpha.2) (2025-02-24)


### Bug Fixes

* 🐛 uniapp renderer ([717debf](https://gitee.com/newgateway/vtj/commits/717debf345cee5168f88124e351ca92aa70a3d0d))





## [0.10.1-alpha.1](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.1-alpha.1) (2025-02-24)


### Bug Fixes

* 🐛 uniapp mock ([1cf64a8](https://gitee.com/newgateway/vtj/commits/1cf64a88b69336c59db9233dbc24bf31ccdc4ecd))





## [0.10.1-alpha.0](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.1-alpha.0) (2025-02-20)


### Features

* ✨ uniapp 支持 uni-ui ([1ee244c](https://gitee.com/newgateway/vtj/commits/1ee244cd04b300c2bd7e84579ea01e4ec98169b8))





# [0.10.0](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.10.0) (2025-02-19)


### Features

* ✨ 更新依赖 ([096a58b](https://gitee.com/newgateway/vtj/commits/096a58bc912583b89967553fde05850332d4e984))
* ✨ uniapp 支持全局css ([18fb654](https://gitee.com/newgateway/vtj/commits/18fb654e13691b7226b77b6b93379b876d2089a2))





## [0.9.11](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.11) (2025-02-13)

**Note:** Version bump only for package create-vtj





## [0.9.10](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.10) (2025-02-13)


### Bug Fixes

* 🐛 出码页面图标组件不显示 ([a94b50e](https://gitee.com/newgateway/vtj/commits/a94b50e00d189a81cc284e3e162f1714b821f1ed))





## [0.9.9](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.9) (2025-01-14)

**Note:** Version bump only for package create-vtj





## [0.9.8](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.8) (2025-01-14)


### Bug Fixes

* 🐛 h5项目模版 ([eb22ff0](https://gitee.com/newgateway/vtj/commits/eb22ff065d2c16d3d6e5f7c150178007c535d51d))





## [0.9.7](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.7) (2025-01-13)


### Features

* ✨ h5平台 ([0497ccb](https://gitee.com/newgateway/vtj/commits/0497ccbb53f01a537176cae36f69b5d3019fc68e))





## [0.9.6](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.6) (2025-01-06)


### Features

* ✨ @vtj/renderer 与 element-plus 解耦 ([6e63fe8](https://gitee.com/newgateway/vtj/commits/6e63fe81bf5e7047f1c621277b9104d2b44b02bd))





## [0.9.5](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.5) (2025-01-04)

**Note:** Version bump only for package create-vtj





## [0.9.4](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.4) (2025-01-03)

**Note:** Version bump only for package create-vtj





## [0.9.3](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.3) (2025-01-03)

**Note:** Version bump only for package create-vtj





## [0.9.2](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.2) (2025-01-03)

**Note:** Version bump only for package create-vtj





## [0.9.1](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.1) (2024-12-17)

**Note:** Version bump only for package create-vtj





# [0.9.0](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.0) (2024-12-16)

**Note:** Version bump only for package create-vtj





# [0.9.0-alpha.7](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.0-alpha.7) (2024-12-16)


### Bug Fixes

* 🐛 simulator style ([08af11b](https://gitee.com/newgateway/vtj/commits/08af11b3e4a7ba310eff971a3f5c871c5b501d18))





# [0.9.0-alpha.6](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.0-alpha.6) (2024-12-14)


### Bug Fixes

* 🐛 NodeEnv.Development ([a791d74](https://gitee.com/newgateway/vtj/commits/a791d74b6f2fcc57394bf501b75b7dd1aaedfc01))





# [0.9.0-alpha.5](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.0-alpha.5) (2024-11-27)

**Note:** Version bump only for package create-vtj





# [0.9.0-alpha.4](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.0-alpha.4) (2024-11-22)


### Features

* ✨ data-item add active hover paadding prop settings ([833c52f](https://gitee.com/newgateway/vtj/commits/833c52fbecafb1c5fc02d71999db6af287b5f2e9))





# [0.9.0-alpha.3](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.0-alpha.3) (2024-11-22)


### Bug Fixes

* 🐛 project ignore .vtj/histories ([a6f0e14](https://gitee.com/newgateway/vtj/commits/a6f0e1422ad51dde551d5731c1221c0d457469f9))





# [0.9.0-alpha.2](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.0-alpha.2) (2024-11-20)


### Bug Fixes

* 🐛 project template ([8b72f9b](https://gitee.com/newgateway/vtj/commits/8b72f9b78dc2ab1fc0695806178b8114fbc675fb))
* 🐛 template project add clean script ([b209526](https://gitee.com/newgateway/vtj/commits/b2095262aacc54506c6d44c01bee03d91dd39d01))





# [0.9.0-alpha.1](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.0-alpha.1) (2024-11-18)


### Bug Fixes

* 🐛 link error ([faef175](https://gitee.com/newgateway/vtj/commits/faef175bd44b52fdf4d84644e2dce0ad70be232d))





# [0.9.0-alpha.0](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.9.0-alpha.0) (2024-11-18)

**Note:** Version bump only for package create-vtj





## [0.8.23](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.23) (2024-10-15)


### Bug Fixes

* 🐛 app template ([a92f512](https://gitee.com/newgateway/vtj/commits/a92f512fb3b78bdca96c79ce1b595aba76e77b51))






## [0.8.22](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.22) (2024-10-11)

**Note:** Version bump only for package create-vtj






## [0.8.21](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.21) (2024-09-16)

**Note:** Version bump only for package create-vtj






## [0.8.20](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.20) (2024-09-14)

**Note:** Version bump only for package create-vtj






## [0.8.19](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.19) (2024-09-12)


### Bug Fixes

* 🐛 cli  template type error ([2f25df2](https://gitee.com/newgateway/vtj/commits/2f25df2956d9879c53152b7908020afdf6ddb14f))






## [0.8.18](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.18) (2024-09-06)

**Note:** Version bump only for package create-vtj






## [0.8.17](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.17) (2024-08-13)


### Bug Fixes

* 🐛 项目工程 tsconfig.ts exclude .vtj ([c064e2e](https://gitee.com/newgateway/vtj/commits/c064e2e0e385f91e6c5c4b2107b4fd41c456b0b9))






## [0.8.16](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.16) (2024-07-22)

**Note:** Version bump only for package create-vtj





## [0.8.15](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.15) (2024-07-19)


### Bug Fixes

* 🐛 修复项目模板样式丢失 ([1ed5f3d](https://gitee.com/newgateway/vtj/commits/1ed5f3d6842545aba045eb361201fca6bbbfc708))





## [0.8.14](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.14) (2024-07-12)

**Note:** Version bump only for package create-vtj





## [0.8.13](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.13) (2024-07-09)

**Note:** Version bump only for package create-vtj





## [0.8.12](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.12) (2024-07-09)

**Note:** Version bump only for package create-vtj





## [0.8.11](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.11) (2024-07-03)

**Note:** Version bump only for package create-vtj





## [0.8.10](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.10) (2024-06-27)

**Note:** Version bump only for package create-vtj





## [0.8.9](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.9) (2024-06-06)

**Note:** Version bump only for package create-vtj





## [0.8.8](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.8) (2024-05-23)


### Bug Fixes

* 🐛 provider install ([587112d](https://gitee.com/newgateway/vtj/commits/587112d873cb5738691be63b269d16e04ae9312e))


### Features

* ✨ ui support ADAPTER_KEY_STRING ([44cf31d](https://gitee.com/newgateway/vtj/commits/44cf31dee87f4896b4071b688e35eddbf5a96b1c))





## [0.8.7](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.7) (2024-05-14)


### Features

* ✨ ui support ADAPTER_KEY_STRING ([44cf31d](https://gitee.com/newgateway/vtj/commits/44cf31dee87f4896b4071b688e35eddbf5a96b1c))





## [0.8.6](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.6) (2024-05-07)

**Note:** Version bump only for package create-vtj





## [0.8.5](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.5) (2024-05-06)


### Bug Fixes

* 🐛 create-vtj options.name ([46cf35e](https://gitee.com/newgateway/vtj/commits/46cf35ec573e10f8869edd4445037e49bfb09092))





## [0.8.4](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.4) (2024-05-06)


### Features

* ✨ auto plugins from package.json ([f327858](https://gitee.com/newgateway/vtj/commits/f3278585be56c841b672745bba5be780f26fb054))






## [0.8.3](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.3) (2024-04-23)

**Note:** Version bump only for package create-vtj






## [0.8.2](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.2) (2024-04-22)


### Bug Fixes

* 🐛 编译后不应该调service.init; 出码$props 改为 props ([b3ab003](https://gitee.com/newgateway/vtj/commits/b3ab003c59df81225da8b0a43593f2b28f7bf53b))





## [0.8.1](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.1) (2024-04-22)


### Bug Fixes

* 🐛 cli template ([911c3a0](https://gitee.com/newgateway/vtj/commits/911c3a0e2bb60548affe5dcf5a496577809d63b8))
* 🐛 cli templates deps ([e509c3f](https://gitee.com/newgateway/vtj/commits/e509c3fee7d360654944dab79b482dee133da638))
* 🐛 cli templates version ([91ad22f](https://gitee.com/newgateway/vtj/commits/91ad22f2cf5f27f7a660a8fabe27ebffe80772d2))






# [0.8.0](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.8.0) (2024-04-22)


### Features

* ✨ cli 支持 插件项目 ([f8a61c9](https://gitee.com/newgateway/vtj/commits/f8a61c97f7e94a6a4afd23e91c6d2b879cf8eaa3))
* ✨ cli add buildEnd ([3682809](https://gitee.com/newgateway/vtj/commits/368280984975733948c824ccab64624b1de8bd30))






## [0.7.24](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.7.24) (2024-04-10)

**Note:** Version bump only for package create-vtj






## [0.7.23](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.7.23) (2024-04-08)

**Note:** Version bump only for package create-vtj






## [0.7.22](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.7.22) (2024-04-03)

**Note:** Version bump only for package create-vtj






## [0.7.21](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.7.21) (2024-04-03)


### Features

* ✨ api mock ([df7400f](https://gitee.com/newgateway/vtj/commits/df7400f1c2f7aa20f24e5217b177a38877de5cdd))






## [0.7.21](https://gitee.com/newgateway/vtj/compare/<EMAIL>-vtj@0.7.21) (2024-04-03)


### Features

* ✨ api mock ([df7400f](https://gitee.com/newgateway/vtj/commits/df7400f1c2f7aa20f24e5217b177a38877de5cdd))
