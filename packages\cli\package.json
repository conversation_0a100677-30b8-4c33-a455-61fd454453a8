{"name": "@vtj/cli", "private": false, "version": "0.12.10", "type": "module", "keywords": ["AI+低代码", "低代码引擎", "LowCode Engine", "Vue3低代码", "低代码渲染器", "低代码设计器", "代码生成器", "代码可视化"], "description": "VTJ是一款以AI驱动的Vue3前端低代码开发工具。内置低代码引擎、渲染器和代码生成器，支持Vue源码与低代码DSL双向转换，面向前端开发者，开箱即用。 无缝嵌入本地开发工程，不改变前端开发流程和编码习惯。", "repository": {"type": "git", "url": "https://gitee.com/newgateway/vtj.git"}, "homepage": "https://gitee.com/newgateway/vtj", "author": "chenhuachun", "license": "MIT", "bin": {"vtj": "./bin/vtj.js"}, "scripts": {"build": "unbuild", "test": "vitest run", "vitest": "vitest", "coverage": "vitest run --coverage"}, "engines": {"node": ">=20.0.0"}, "dependencies": {"@babel/core": "~7.28.0", "@babel/preset-env": "~7.28.0", "@babel/types": "~7.28.0", "@rollup/plugin-babel": "~6.0.4", "@types/babel__core": "~7.20.5", "@types/body-parser": "~1.19.5", "@types/node": "~22.15.0", "@types/serve-static": "~1.15.5", "@vitejs/plugin-basic-ssl": "~2.1.0", "@vitejs/plugin-legacy": "~6.1.0", "@vitejs/plugin-vue": "~5.2.0", "@vitejs/plugin-vue-jsx": "~4.2.0", "@vitest/coverage-v8": "~3.2.0", "@vtj/node": "workspace:~", "@vue/test-utils": "~2.4.5", "body-parser": "~1.20.2", "cross-env": "~7.0.3", "jsdom": "~26.1.0", "memfs": "~4.17.0", "rollup-plugin-external-globals": "~0.13.0", "rollup-plugin-visualizer": "~5.14.0", "sass": "~1.89.0", "serve-static": "~1.16.0", "terser": "~5.43.0", "typescript": "~5.8.2", "unbuild": "~2.0.0", "unplugin-element-plus": "~0.9.0", "vite": "~6.3.0", "vite-plugin-cdn-import": "~1.0.1", "vite-plugin-dts": "~4.5.0", "vite-plugin-node-polyfills": "~0.24.0", "vite-plugin-vue-devtools": "~7.7.0", "vitest": "~3.2.3", "vue-tsc": "~2.2.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "bin", "config"], "gitHead": "d03843144f07c2d98c1e0c72c8c6eb1117c01722", "publishConfig": {"access": "public"}}