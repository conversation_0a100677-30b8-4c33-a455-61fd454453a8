/**
 * 认证服务
 * 提供用户认证相关的功能
 */
import axios from 'axios';

export interface User {
  id: string;
  username: string;
  email?: string;
  roles?: string[];
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  message?: string;
  user?: User;
  token?: string;
}

class AuthService {
  private currentUser: User | null = null;
  private token: string | null = null;

  /**
   * 初始化认证服务
   */
  init() {
    // 从localStorage恢复用户信息
    const savedUser = localStorage.getItem('user');
    const savedToken = localStorage.getItem('token');

    if (savedUser && savedToken) {
      this.currentUser = JSON.parse(savedUser);
      this.token = savedToken;
    }
  }

  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      // 调用真实的登录API
      const response = await axios.post('/api/login', credentials, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
          'Accept': 'application/json, text/javascript, *' + '/*; q=0.01',
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      console.log('登录响应:', response);

      if (response.data && response.data.success) {
        const user: User = {
          id: response.data.user.id,
          username: response.data.user.username,
          email: response.data.user.email,
          roles: response.data.user.roles || ['user']
        };

        this.currentUser = user;
        this.token = response.data.token;

        // 保存到localStorage
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('token', this.token);

        return {
          success: true,
          message: '登录成功',
          user: user,
          token: this.token
        };
      } else {
        return {
          success: false,
          message: response.data?.message || '登录失败'
        };
      }
    } catch (error) {
      console.error('登录失败:', error);
      return {
        success: false,
        message: '登录失败，请检查网络连接和服务器状态'
      };
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    this.currentUser = null;
    this.token = null;

    // 清除localStorage
    localStorage.removeItem('user');
    localStorage.removeItem('token');
  }

  /**
   * 获取当前用户
   */
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  /**
   * 获取认证token
   */
  getToken(): string | null {
    return this.token;
  }

  /**
   * 检查是否已认证
   */
  isAuthenticated(): boolean {
    return this.currentUser !== null && this.token !== null;
  }

  /**
   * 检查用户权限
   */
  hasRole(role: string): boolean {
    return this.currentUser?.roles?.includes(role) ?? false;
  }
}

// 导出单例实例
export const authService = new AuthService();

// 默认导出
export default authService;
