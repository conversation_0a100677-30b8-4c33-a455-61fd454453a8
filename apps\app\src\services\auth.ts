/**
 * 认证服务
 * 提供用户认证相关的功能
 */

export interface User {
  id: string;
  username: string;
  email?: string;
  roles?: string[];
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  message?: string;
  user?: User;
  token?: string;
}

class AuthService {
  private currentUser: User | null = null;
  private token: string | null = null;

  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      // 这里应该调用实际的登录API
      // 目前返回模拟数据
      const mockUser: User = {
        id: '1',
        username: credentials.username,
        email: `${credentials.username}@example.com`,
        roles: ['user']
      };

      this.currentUser = mockUser;
      this.token = 'mock-jwt-token';

      return {
        success: true,
        message: '登录成功',
        user: mockUser,
        token: this.token
      };
    } catch (error) {
      return {
        success: false,
        message: '登录失败'
      };
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    this.currentUser = null;
    this.token = null;
  }

  /**
   * 获取当前用户
   */
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  /**
   * 获取认证token
   */
  getToken(): string | null {
    return this.token;
  }

  /**
   * 检查是否已认证
   */
  isAuthenticated(): boolean {
    return this.currentUser !== null && this.token !== null;
  }

  /**
   * 检查用户权限
   */
  hasRole(role: string): boolean {
    return this.currentUser?.roles?.includes(role) ?? false;
  }
}

// 导出单例实例
export const authService = new AuthService();

// 默认导出
export default authService;
