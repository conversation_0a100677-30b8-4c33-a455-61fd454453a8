/**
 * 管理系统 API 配置
 * 这些 API 来自外部网站，通过代理访问
 */
import { createApi } from '@vtj/utils';

// ==================== 患者订单相关 API ====================

/**
 * 获取今日订单列表
 */
export const getAllOrderTodayList = createApi<any>({
  url: '/Manage/PatientOrder/GetAllOrderTodayList',
  method: 'get'
});

/**
 * 获取历史订单列表
 */
export const getAllOrderHistoryList = createApi<any>({
  url: '/Manage/PatientOrder/GetAllOrderHistoryList',
  method: 'get'
});

// ==================== 菜单管理相关 API ====================

/**
 * 获取父级菜单
 */
export const getParentMenus = createApi<any>({
  url: '/Manage/Menus/GetParentMenus',
  method: 'get'
});

/**
 * 获取子菜单列表
 */
export const getMenusChildrenList = createApi<any>({
  url: '/Manage/Menus/GetMenusChildrenList',
  method: 'get'
});

// ==================== 组织架构相关 API ====================

/**
 * 获取组织架构列表
 */
export const getOrganStructList = createApi<any>({
  url: '/Manage/OrganStruct/GetOrganStructList',
  method: 'get'
});

// ==================== 系统设置相关 API ====================

/**
 * 获取系统设置数据
 */
export const getSystemSetData = createApi<any>({
  url: '/Manage/KeyValue/GetSystemSetData',
  method: 'get'
});

// ==================== 类型定义 ====================

export interface OrderItem {
  id: number;
  patientName: string;
  orderDate: string;
  status: string;
}

export interface MenuItem {
  id: number;
  name: string;
  icon?: string;
  path: string;
  parentId?: number;
}

export interface OrganStruct {
  id: number;
  name: string;
  level: number;
  parentId?: number;
}

export interface SystemSettings {
  systemName: string;
  version: string;
  theme: string;
  [key: string]: any;
}

// ==================== 响应类型 ====================

export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// ==================== 导出所有 API ====================

export const manageApis = {
  // 订单相关
  getAllOrderTodayList,
  getAllOrderHistoryList,
  
  // 菜单相关
  getParentMenus,
  getMenusChildrenList,
  
  // 组织架构
  getOrganStructList,
  
  // 系统设置
  getSystemSetData
};

export default manageApis;
