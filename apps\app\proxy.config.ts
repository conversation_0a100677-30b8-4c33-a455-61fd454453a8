// 请将下面的 API 地址替换为您实际要使用的 API 服务器地址
// 例如：const API_BASE_URL = 'https://hospital-api.example.com';
const API_BASE_URL = 'https://your-actual-api-server.com';

export default {
  '/api': {
    target: API_BASE_URL,
    changeOrigin: true,
    ws: true,
    secure: true, // 如果是 HTTPS，设置为 true；如果有证书问题，设置为 false
    headers: {
      // 如果 API 需要认证，请取消注释并填入正确的值
      // 'Authorization': 'Bearer your-token',
      // 'X-API-Key': 'your-api-key',
      // 'Content-Type': 'application/json'
    }
  },
  '/Manage': {
    target: API_BASE_URL,
    changeOrigin: true,
    ws: true,
    secure: true, // 如果是 HTTPS，设置为 true；如果有证书问题，设置为 false
    rewrite: (path: string) => {
      console.log('代理请求:', path);
      // 如果需要重写路径，可以在这里修改
      // 例如：return path.replace(/^\/Manage/, '/api/v1');
      return path;
    },
    headers: {
      // 如果 API 需要认证，请取消注释并填入正确的值
      // 'Authorization': 'Bearer your-token',
      // 'X-API-Key': 'your-api-key',
      // 'Content-Type': 'application/json'
    }
  },
  '/Account': {
    target: API_BASE_URL,
    changeOrigin: true,
    ws: true,
    secure: true, // 如果是 HTTPS，设置为 true；如果有证书问题，设置为 false
    headers: {
      // 如果 API 需要认证，请取消注释并填入正确的值
      // 'Authorization': 'Bearer your-token',
      // 'X-API-Key': 'your-api-key',
      // 'Content-Type': 'application/json'
    }
  }
};
