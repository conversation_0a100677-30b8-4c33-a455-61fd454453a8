// API 服务器地址配置
const API_BASE_URL = 'http://192.168.80.79:8000';

export default {
  '/api': {
    target: API_BASE_URL,
    changeOrigin: true,
    ws: true,
    secure: false, // HTTP 服务器设置为 false
    headers: {
      // 如果 API 需要认证，请取消注释并填入正确的值
      // 'Authorization': 'Bearer your-token',
      // 'X-API-Key': 'your-api-key',
      // 'Content-Type': 'application/json'
    }
  },
  '/Manage': {
    target: API_BASE_URL,
    changeOrigin: true,
    ws: true,
    secure: false, // HTTP 服务器设置为 false
    rewrite: (path: string) => {
      console.log('代理请求:', path);
      // 如果需要重写路径，可以在这里修改
      // 例如：return path.replace(/^\/Manage/, '/api/v1');
      return path;
    },
    headers: {
      // 如果 API 需要认证，请取消注释并填入正确的值
      // 'Authorization': 'Bearer your-token',
      // 'X-API-Key': 'your-api-key',
      // 'Content-Type': 'application/json'
    }
  },
  '/Account': {
    target: API_BASE_URL,
    changeOrigin: true,
    ws: true,
    secure: false, // HTTP 服务器设置为 false
    headers: {
      // 如果 API 需要认证，请取消注释并填入正确的值
      // 'Authorization': 'Bearer your-token',
      // 'X-API-Key': 'your-api-key',
      // 'Content-Type': 'application/json'
    }
  }
};
