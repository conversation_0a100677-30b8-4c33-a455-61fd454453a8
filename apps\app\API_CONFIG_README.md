# API 配置说明

## 概述

本项目使用外部 API 服务，通过 Vite 代理进行访问。主要涉及以下 API 端点：

- `/Manage/PatientOrder/GetAllOrderTodayList` - 获取今日订单列表
- `/Manage/PatientOrder/GetAllOrderHistoryList` - 获取历史订单列表  
- `/Manage/Menus/GetParentMenus` - 获取父级菜单
- `/Manage/Menus/GetMenusChildrenList` - 获取子菜单列表
- `/Manage/OrganStruct/GetOrganStructList` - 获取组织架构列表
- `/Manage/KeyValue/GetSystemSetData` - 获取系统设置数据

## 配置步骤

### 1. 修改代理配置

编辑 `apps/app/proxy.config.ts` 文件：

```typescript
// 将此处替换为您的实际 API 服务器地址
const API_BASE_URL = 'https://your-actual-api-server.com';
```

### 2. 配置认证（如果需要）

如果您的 API 需要认证，请在 `proxy.config.ts` 中取消注释并配置相关 headers：

```typescript
headers: {
  'Authorization': 'Bearer your-api-token',
  'X-API-Key': 'your-api-key',
  'Content-Type': 'application/json'
}
```

### 3. 路径重写（如果需要）

如果外部 API 的路径结构与项目中使用的不同，可以使用 `rewrite` 功能：

```typescript
rewrite: (path: string) => {
  // 示例：将 /Manage/PatientOrder/GetAllOrderTodayList 
  // 重写为 /api/v1/patient-orders/today
  return path.replace(/^\/Manage/, '/api/v1');
}
```

## 使用方式

### 在代码中使用

```typescript
import { getAllOrderTodayList, getParentMenus } from '@/api/manage';

// 获取今日订单
const todayOrders = await getAllOrderTodayList();

// 获取菜单
const menus = await getParentMenus();
```

### 在 VTJ 页面中使用

这些 API 会自动通过代理转发到配置的服务器，无需额外配置。

## 常见问题

### 1. CORS 错误

确保代理配置中设置了 `changeOrigin: true`：

```typescript
{
  target: 'https://your-api.com',
  changeOrigin: true,  // 重要！
  ws: true,
  secure: true
}
```

### 2. HTTPS 证书问题

如果目标服务器使用自签名证书，可以设置：

```typescript
{
  target: 'https://your-api.com',
  changeOrigin: true,
  secure: false,  // 忽略 SSL 证书验证（仅开发环境）
}
```

### 3. 认证失败

检查 headers 配置是否正确，确保 API 密钥或 token 有效。

### 4. 路径不匹配

使用浏览器开发者工具查看实际发送的请求路径，并相应调整 `rewrite` 函数。

## 配置示例

详细的配置示例请参考 `proxy.config.example.ts` 文件。

## 测试

配置完成后，重启开发服务器：

```bash
npm run dev
```

查看浏览器控制台和终端输出，确认 API 请求是否正常转发。
