{"name": "@vtj/parser", "private": false, "version": "0.12.70", "type": "module", "keywords": ["低代码引擎", "LowCode Engine", "Vue3低代码", "低代码渲染器", "低代码设计器", "代码生成器", "代码可视化"], "description": "VTJ 是一款基于 Vue3 + Typescript 的低代码页面可视化设计器。内置低代码引擎、渲染器和代码生成器，面向前端开发者，开箱即用。 无缝嵌入本地开发工程，不改变前端开发流程和编码习惯。", "repository": {"type": "git", "url": "https://gitee.com/newgateway/vtj.git"}, "homepage": "https://gitee.com/newgateway/vtj", "author": "chenhuachun", "license": "MIT", "scripts": {"build": "vue-tsc && vite build", "test": "vitest run", "vitest": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"@babel/generator": "~7.28.0", "@babel/parser": "~7.28.0", "@babel/traverse": "~7.28.0", "@babel/types": "~7.28.0", "@vtj/base": "workspace:~", "@vtj/coder": "workspace:~", "@vtj/core": "workspace:~", "@vue/compiler-dom": "~3.5.13", "@vue/compiler-sfc": "~3.5.13", "htmlparser2": "~10.0.0", "postcss": "~8.5.0", "sass": "~1.89.0"}, "devDependencies": {"@vtj/cli": "workspace:~"}, "exports": {".": {"types": "./types/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./types/index.d.ts", "files": ["dist", "types"], "gitHead": "d03843144f07c2d98c1e0c72c8c6eb1117c01722", "publishConfig": {"access": "public"}}