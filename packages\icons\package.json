{"name": "@vtj/icons", "private": false, "version": "0.12.70", "type": "module", "keywords": ["低代码引擎", "LowCode Engine", "Vue3低代码", "低代码渲染器", "低代码设计器", "代码生成器", "代码可视化"], "description": "VTJ 是一款基于 Vue3 + Typescript 的低代码页面可视化设计器。内置低代码引擎、渲染器和代码生成器，面向前端开发者，开箱即用。 无缝嵌入本地开发工程，不改变前端开发流程和编码习惯。", "repository": {"type": "git", "url": "https://gitee.com/newgateway/vtj.git"}, "homepage": "https://gitee.com/newgateway/vtj", "author": "chenhuachun", "license": "MIT", "scripts": {"svg2vue": "node scripts/svg.mjs", "icons": "node scripts/icons.mjs", "assets": "node scripts/assets.mjs", "gen": "npm run svg2vue && npm run icons", "build": "npm run gen && npm run assets && vue-tsc && vite build && npm run build:umd", "build:umd": "cross-env UMD=true vite build", "test": "npm run gen && vitest run", "vitest": "npm run gen && vitest", "coverage": "vitest run --coverage"}, "dependencies": {"@element-plus/icons-vue": "~2.3.1"}, "devDependencies": {"@vtj/cli": "workspace:~", "vue": "~3.5.5", "xml2js": "~0.6.2"}, "exports": {"./dist/style.css": "./dist/style.css", ".": {"types": "./types/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.umd.js"}}, "main": "./dist/index.umd.js", "module": "./dist/index.mjs", "types": "./types/index.d.ts", "files": ["dist", "types", "assets"], "gitHead": "d03843144f07c2d98c1e0c72c8c6eb1117c01722", "publishConfig": {"access": "public"}}