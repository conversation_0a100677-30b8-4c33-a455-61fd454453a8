{"name": "@vtj/docs", "private": true, "version": "0.12.70", "type": "module", "scripts": {"docs:dev": "vitepress dev", "docs:build": "npm run empty:dist && vitepress build --target es2015", "docs:preview": "vitepress preview", "build:gitee": "npm run empty:dist && cross-env BUILD_TYPE=gitee vitepress build", "empty:dist": "vtj rm dist"}, "dependencies": {"@vitepress-demo-preview/component": "~2.3.0", "@vitepress-demo-preview/plugin": "~1.4.0", "@vtj/icons": "workspace:*", "@vtj/pro": "workspace:*", "@vtj/renderer": "workspace:*", "@vtj/ui": "workspace:*", "@vtj/utils": "workspace:*", "@vtj/web": "workspace:*", "vue": "~3.5.5", "vue-router": "~4.5.0"}, "devDependencies": {"@vtj/cli": "workspace:*", "vitepress": "^1.4.0"}, "gitHead": "b65d1b19322cbd0721173d094db1b5ece018d980"}