{"name": "@vtj/ui", "private": false, "version": "0.12.70", "type": "module", "keywords": ["低代码引擎", "LowCode Engine", "Vue3低代码", "低代码渲染器", "低代码设计器", "代码生成器", "代码可视化"], "description": "VTJ 是一款基于 Vue3 + Typescript 的低代码页面可视化设计器。内置低代码引擎、渲染器和代码生成器，面向前端开发者，开箱即用。 无缝嵌入本地开发工程，不改变前端开发流程和编码习惯。", "repository": {"type": "git", "url": "https://gitee.com/newgateway/vtj.git"}, "homepage": "https://gitee.com/newgateway/vtj", "author": "chenhuachun", "license": "MIT", "scripts": {"build": "vue-tsc && vite build && npm run build:umd", "build:umd": "cross-env UMD=true vite build", "test": "vitest run", "vitest": "vitest", "coverage": "vitest run --coverage"}, "engines": {"node": ">=16.0.0"}, "dependencies": {"@vtj/icons": "workspace:~", "@vtj/utils": "workspace:~", "@vueuse/core": "~13.5.0", "element-plus": "~2.10.0", "sortablejs": "~1.15.6", "vxe-table": "~4.6.17", "vxe-table-plugin-menus": "~4.0.3"}, "devDependencies": {"@types/qrcode": "^1.5.5", "@types/sortablejs": "~1.15.8", "@vtj/cli": "workspace:~", "qrcode": "~1.5.3", "vue": "~3.5.5", "vue-router": "~4.5.0"}, "files": ["dist", "types"], "main": "dist/index.umd.js", "module": "dist/index.mjs", "types": "types/index.d.ts", "exports": {"./dist/style.css": "./dist/style.css", ".": {"types": "./types/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.umd.js"}}, "publishConfig": {"access": "public"}, "gitHead": "d03843144f07c2d98c1e0c72c8c6eb1117c01722"}