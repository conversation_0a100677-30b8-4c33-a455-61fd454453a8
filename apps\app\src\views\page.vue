<template>
  <div class="home-container">
    <div class="welcome-section">
      <h1>欢迎使用 VTJ 低代码平台</h1>
      <p>这是一个强大的可视化开发平台，帮助您快速构建应用程序。</p>
    </div>

    <div class="features-section">
      <div class="feature-card">
        <h3>可视化设计</h3>
        <p>拖拽式界面设计，无需编写代码即可创建美观的用户界面</p>
      </div>

      <div class="feature-card">
        <h3>组件丰富</h3>
        <p>内置大量常用组件，满足各种业务场景需求</p>
      </div>

      <div class="feature-card">
        <h3>快速开发</h3>
        <p>提高开发效率，缩短项目交付周期</p>
      </div>
    </div>

    <div class="quick-start">
      <h2>快速开始</h2>
      <p>点击左侧菜单开始探索各种功能模块</p>
    </div>
  </div>
</template>

<script setup lang="ts">
// 首页组件
</script>

<style scoped>
.home-container {
  padding: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: 60px;
}

.welcome-section h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 20px;
}

.welcome-section p {
  font-size: 1.2rem;
  color: #7f8c8d;
  line-height: 1.6;
}

.features-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}

.feature-card {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card h3 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 15px;
}

.feature-card p {
  color: #7f8c8d;
  line-height: 1.6;
}

.quick-start {
  text-align: center;
  background: #f8f9fa;
  padding: 40px;
  border-radius: 10px;
}

.quick-start h2 {
  font-size: 2rem;
  color: #2c3e50;
  margin-bottom: 15px;
}

.quick-start p {
  font-size: 1.1rem;
  color: #7f8c8d;
}
</style>
