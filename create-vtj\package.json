{"name": "create-vtj", "private": false, "version": "0.12.8", "type": "module", "keywords": ["低代码引擎", "LowCode Engine", "Vue3低代码", "低代码渲染器", "低代码设计器", "代码生成器", "代码可视化"], "description": "VTJ 是一款基于 Vue3 + Typescript 的低代码页面可视化设计器。内置低代码引擎、渲染器和代码生成器，面向前端开发者，开箱即用。 无缝嵌入本地开发工程，不改变前端开发流程和编码习惯。", "repository": {"type": "git", "url": "https://gitee.com/newgateway/vtj.git"}, "homepage": "https://gitee.com/newgateway/vtj", "author": "chenhuachun", "license": "MIT", "scripts": {"build": "unbuild"}, "dependencies": {"@vtj/node": "workspace:~", "cross-spawn": "~7.0.3", "kolorist": "~1.8.0", "minimist": "~1.2.8", "prompts": "~2.4.2"}, "devDependencies": {"@types/minimist": "~1.2.5", "@types/prompts": "~2.4.8", "unbuild": "~2.0.0"}, "main": "index.js", "bin": {"create-vtj": "index.js"}, "files": ["dist", "templates", "index.js", ".giti<PERSON>re", "**/.g<PERSON><PERSON><PERSON>"], "gitHead": "d03843144f07c2d98c1e0c72c8c6eb1117c01722", "publishConfig": {"access": "public"}}