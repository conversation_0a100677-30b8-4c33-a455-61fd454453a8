{"name": "vtj-project-app", "description": "项目示例", "private": true, "version": "0.12.70", "type": "module", "scripts": {"setup": "pnpm install --unsafe-perm --registry=https://registry.npmmirror.com", "dev": "cross-env ENV_TYPE=local vite", "build": "pnpm run build:prod", "build:dev": "vue-tsc && cross-env ENV_TYPE=dev vite build", "build:sit": "vue-tsc && cross-env ENV_TYPE=sit vite build", "build:uat": "vue-tsc && cross-env ENV_TYPE=uat vite build", "build:pre": "vue-tsc && cross-env ENV_TYPE=pre vite build", "build:prod": "vue-tsc && cross-env ENV_TYPE=live vite build", "preview": "vite preview", "mock": "node mock-server.js"}, "dependencies": {"@vtj/example-ui": "~0.2.0", "@vtj/plugin-ckeditor": "~0.2.1", "@vtj/web": "workspace:~", "pinia": "~3.0.2", "vue": "~3.5.5", "vue-router": "~4.5.0"}, "devDependencies": {"@vtj/charts": "workspace:~", "@vtj/cli": "workspace:~", "@vtj/icons": "workspace:~", "@vtj/pro": "workspace:~", "@vtj/renderer": "workspace:~", "@vtj/ui": "workspace:~", "@vtj/utils": "workspace:~"}}