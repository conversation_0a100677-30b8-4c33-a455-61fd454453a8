{"name": "dev-web", "private": true, "version": "0.12.70", "type": "module", "scripts": {"dev": "cross-env ENV_TYPE=local vite --force", "dev:uni": "cross-env uni=true vite --force", "build": "pnpm run build:prod", "build:dev": "vue-tsc && cross-env ENV_TYPE=dev vite build", "build:uni": "vue-tsc && cross-env ENV_TYPE=dev uni=true vite build", "build:sit": "vue-tsc && cross-env ENV_TYPE=sit vite build", "build:uat": "vue-tsc && cross-env ENV_TYPE=uat vite build", "build:pre": "vue-tsc && cross-env ENV_TYPE=pre vite build", "build:prod": "vue-tsc && cross-env ENV_TYPE=live vite build", "build:ext": "cross-env Extension=true vite build", "preview": "vite preview"}, "engines": {"node": ">=20.0.0"}, "dependencies": {"@vtj/core": "workspace:~", "@vtj/icons": "workspace:~", "@vtj/materials": "workspace:~", "@vtj/parser": "workspace:~", "@vtj/pro": "workspace:~", "@vtj/renderer": "workspace:~", "@vtj/ui": "workspace:~", "@vtj/uni": "workspace:~", "@vtj/utils": "workspace:~", "@vtj/web": "workspace:~", "vue": "~3.5.5", "vue-router": "~4.5.0"}, "devDependencies": {"@vtj/cli": "workspace:~", "@vtj/local": "workspace:~", "@vtj/plugin-ckeditor": "~0.2.1"}, "description": "> TODO: description", "author": "陈华春 <<EMAIL>>", "homepage": "", "license": "ISC"}