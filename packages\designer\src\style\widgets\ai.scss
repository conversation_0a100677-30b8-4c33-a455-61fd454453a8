@use '../core' as *;

@include b(ai-widget) {
  > .x-panel__body {
    position: relative;
  }
  .footer {
    text-align: center;
    font-size: 12px;
    opacity: 0.7;
    border-top: 1px solid var(--el-border-color-lighter);
    padding-top: 10px;
  }
  @include e(drawer-modal) {
    position: absolute !important;
    width: 100%;
    height: 100%;
  }
  @include e(drawer) {
    box-shadow: none !important;
    --el-drawer-padding-primary: 0 !important;
  }

  @include e(bubble-list) {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  @include e(input) {
    text-align: center;
    .new-btn {
      margin-bottom: 10px;
    }
  }

  @include e(mask) {
    position: absolute;
    inset: 0;
    background-color: var(--el-mask-color-extra-light);
    z-index: 10;
  }
  @include e(wrapper) {
    position: relative;
  }
}

@include b(ai-widget-record) {
  .new-btn {
    width: 100%;
  }

  @include e(list) {
    .v-item {
      margin-bottom: 10px;
    }
    .v-item__title {
      flex-shrink: 1;
    }
    .v-item__content {
      line-height: 1.5em;
      font-size: 12px;
    }
  }
}

@include b(ai-widget-input) {
  position: relative;

  .el-textarea__inner {
    padding-bottom: 45px;
  }

  @include e(footer) {
    display: flex;
    align-items: center;
    gap: 8px;
    position: absolute;
    width: 100%;
    bottom: 0;
    padding: 5px;
    border-top: 1px solid var(--el-border-color-lighter);
    .el-select {
      width: 120px;
    }
    .el-select__wrapper:not(.is-disabled) {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset !important;
      color: var(--el-color-primary);
    }
    .el-select__input,
    .el-select__placeholder {
      color: var(--el-color-primary);
    }
    .el-button {
      margin-left: auto;
    }
  }

  @include e(llm-item) {
    display: flex;
    justify-content: space-between;
    justify-items: center;
    .label {
      padding-right: 20px;
    }
  }
}

@include b(ai-widget-bubble) {
  display: flex;
  column-gap: 8px;
  pre {
    white-space: pre-wrap;
    margin: 0;
  }
  @include e(avatar) {
    flex-shrink: 0;
  }
  @include e(main) {
    font-size: 12px;
    line-height: 1.5em;
    > div {
      margin-bottom: 4px;
    }
  }
  @include e(content) {
    padding: 8px;
    font-size: 12px;
    line-height: 1.5em;
    background-color: var(--el-color-primary-light-9);
    border-radius: 4px;
    position: relative;
    min-height: 32px;
    word-break: break-all;
    & > div.is-collapsed {
      height: 40px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
    p {
      margin: 0;
    }
  }

  @include when(user) {
    justify-content: end;
    flex-direction: row-reverse;
  }

  @include when(ai) {
    @include e(content) {
      background-color: var(--el-color-info-light-9);
      @include when(Failed) {
        background-color: var(--el-color-error-light-9);
      }
      @include when(Error) {
        background-color: var(--el-color-error-light-9);
      }
    }
    @include e(main) {
      width: 100%;
      word-break: break-all;
    }
  }

  @include e(message) {
    width: 100%;
    margin-top: 10px;
    background-color: var(--el-color-error-light-9);
    padding: 10px;
    color: var(--el-color-error);
    border-top: 1px solid var(--el-color-error);
    text-align: right;
    > div {
      text-align: left;
      margin-bottom: 5px;
    }
  }

  @include e(avatar) {
    background: var(--el-color-primary-light-5) !important;
  }

  @include e(tools) {
    justify-content: flex-end;
    display: flex;
    margin-bottom: 4px;
  }

  @include e(loading) {
    position: absolute !important;
    left: -10px;
    bottom: -25px;
  }

  @include e(reasoning) {
    border-left: 3px solid var(--el-border-color-light);
    padding-left: 5px;
    margin-bottom: 5px;
  }
  @include e(toggle) {
    cursor: pointer;
    margin-bottom: 5px;
  }

  @include e(filename) {
    position: absolute;
    width: calc(100% - 40px);
    background-color: var(--el-mask-color);
    border: 1px solid var(--el-border-color-lighter);
    height: 26px;
    line-height: 26px;
    border-radius: 13px;
    text-align: center;
    bottom: 20px;
    margin: 0 10px;
    opacity: 0.6;
    z-index: 1;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding: 0 10px;
    cursor: pointer;
  }
}

@include b(ai-widget-new-chat) {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  @include e(welcome) {
    text-align: center;
    line-height: 1.5em;
    padding: 20px 0;
    > div {
      font-size: 12px;
      margin-top: 10px;
      color: var(--el-text-color-placeholder);
    }
  }

  @include e(input-type) {
    margin-bottom: 8px;
  }
}

@include b(ai-widget-image-input) {
  position: relative;
  background: var(--el-bg-color);
  height: 218px;
  .el-upload-dragger {
    --el-upload-dragger-padding-horizontal: 20px;
    border: 2px dashed var(--el-border-color);
    height: 218px;
    .or {
      font-size: 12px;
      color: var(--el-text-color-placeholder);
      margin: 10px 0;
    }
    .el-upload__text {
      margin-top: 8px;
    }
  }

  @include e(preview) {
    position: absolute;
    inset: 0;
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
    > .el-image {
      width: 100%;
      height: calc(100% - 43px);
      border-radius: var(--el-border-radius-base);
      background-color: var(--el-bg-color);
    }
    .el-loading-spinner {
      transform: translateY(-40px);
    }
    .el-loading-mask {
      background-color: var(--el-mask-color) !important;
    }
  }

  @include e(mask) {
    position: absolute;
    inset: 0;
    background-color: var(--el-overlay-color-lighter);
    border-radius: var(--el-border-radius-base);
  }

  @include e(remove) {
    position: absolute;
    right: 10px;
    top: 10px;
  }

  .v-ai-widget-input__footer {
    background-color: var(--el-bg-color);
    position: absolute;
    bottom: 0;
    border-radius: 0 0 var(--el-border-radius-base) var(--el-border-radius-base);
  }
}

.llm-popper {
  .el-select-dropdown__item {
    height: 26px;
    line-height: 26px;
    padding: 0 14px;
  }
  .el-select-group__title {
    padding: 0 14px;
  }
}

@include b(ai-widget-json-input) {
  .json-name {
    background-color: var(--el-mask-color);
    height: 26px;
    width: calc(100% - 20px);
    position: absolute;
    bottom: 43px;
    z-index: 1;
    text-align: center;
    line-height: 26px;
    font-size: 16px;
    opacity: 0.8;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 13px;
    margin: 0 10px 10px 10px;
  }
  .help-link {
    display: block;
    font-size: 12px;
    text-align: left;
    position: absolute;
    bottom: 5px;
  }
}

@include b(ai-widget-json-input-text) {
  @include e(footer) {
    padding: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    position: absolute;
    width: calc(100% - 2px);
    bottom: 1px;
    padding: 5px;
    border-top: 1px solid var(--el-border-color-lighter);
    background-color: var(--el-mask-color);
    border-radius: 0 0 var(--el-border-radius-base) var(--el-border-radius-base);
    margin: 0 1px;
  }

  &:focusin {
    background-color: red;
  }

  .el-textarea__inner {
    padding-bottom: 45px;
  }
}
