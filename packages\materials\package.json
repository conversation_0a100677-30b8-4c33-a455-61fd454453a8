{"name": "@vtj/materials", "private": false, "version": "0.12.70", "type": "module", "keywords": ["低代码引擎", "LowCode Engine", "Vue3低代码", "低代码渲染器", "低代码设计器", "代码生成器", "代码可视化"], "description": "VTJ 是一款基于 Vue3 + Typescript 的低代码页面可视化设计器。内置低代码引擎、渲染器和代码生成器，面向前端开发者，开箱即用。 无缝嵌入本地开发工程，不改变前端开发流程和编码习惯。", "repository": {"type": "git", "url": "https://gitee.com/newgateway/vtj.git"}, "homepage": "https://gitee.com/newgateway/vtj", "author": "chenhuachun", "license": "MIT", "scripts": {"build": "npm run remove && npm run build:ui && npm run build:icons && npm run build:charts && npm run build:element && npm run build:antdv && npm run build:vant && npm run build:uniApp && npm run build:uniH5 && npm run build:uniH5Vue && npm run build:uniH5C && npm run build:uniUI && npm run build:uniUIC  && npm run copy", "build:ui": "vue-tsc && cross-env BUILD_TYPE=ui vite build", "build:icons": "vue-tsc && cross-env BUILD_TYPE=icons vite build", "build:element": "vue-tsc && cross-env BUILD_TYPE=element vite build", "build:antdv": "vue-tsc && cross-env BUILD_TYPE=antdv vite build", "build:charts": "vue-tsc && cross-env BUILD_TYPE=charts vite build", "build:vant": "vue-tsc && cross-env BUILD_TYPE=vant vite build", "build:uniApp": "vue-tsc && cross-env BUILD_TYPE=uniApp vite build", "build:uniH5": "vue-tsc && cross-env BUILD_TYPE=uniH5 vite build", "build:uniH5Vue": "vue-tsc && cross-env BUILD_TYPE=uniH5Vue vite build", "build:uniH5C": "vue-tsc && cross-env BUILD_TYPE=uniH5C vite build", "build:uniUI": "vue-tsc && cross-env BUILD_TYPE=uniUI vite build", "build:uniUIC": "vue-tsc && cross-env BUILD_TYPE=uniUIC vite build", "remove": "node scripts/rm.mjs", "copy": "node scripts/copy.mjs"}, "devDependencies": {"@dcloudio/uni-app": "3.0.0-4050720250324001", "@dcloudio/uni-h5": "3.0.0-4050720250324001", "@dcloudio/uni-h5-vue": "3.0.0-4050720250324001", "@dcloudio/uni-ui": "~1.5.3", "@vtj/charts": "workspace:~", "@vtj/cli": "workspace:~", "@vtj/core": "workspace:~", "@vtj/icons": "workspace:~", "@vtj/ui": "workspace:~", "@vtj/utils": "workspace:~", "@vueuse/core": "~13.5.0", "ant-design-vue": "~4.2.0", "echarts": "~5.6.0", "element-plus": "~2.10.0", "vant": "~4.9.10", "vue": "~3.5.5", "vue-router": "~4.5.0"}, "files": ["dist", "src"], "gitHead": "d03843144f07c2d98c1e0c72c8c6eb1117c01722", "publishConfig": {"access": "public"}}