const express = require('express');
const cors = require('cors');

const app = express();
const port = 8080;

// 启用 CORS
app.use(cors());
app.use(express.json());

// Mock 数据
const mockData = {
  code: 0,
  message: '成功',
  data: []
};

// Mock API 路由
app.get('/Manage/PatientOrder/GetAllOrderTodayList', (req, res) => {
  console.log('请求今日订单列表');
  res.json({
    ...mockData,
    data: [
      { id: 1, patientName: '张三', orderDate: '2024-01-08', status: '已完成' },
      { id: 2, patientName: '李四', orderDate: '2024-01-08', status: '处理中' }
    ]
  });
});

app.get('/Manage/PatientOrder/GetAllOrderHistoryList', (req, res) => {
  console.log('请求历史订单列表');
  res.json({
    ...mockData,
    data: [
      { id: 1, patientName: '王五', orderDate: '2024-01-07', status: '已完成' },
      { id: 2, patientName: '赵六', orderDate: '2024-01-06', status: '已完成' }
    ]
  });
});

app.get('/Manage/Menus/GetParentMenus', (req, res) => {
  console.log('请求父级菜单');
  res.json({
    ...mockData,
    data: [
      { id: 1, name: '订单管理', icon: 'order', path: '/order' },
      { id: 2, name: '患者管理', icon: 'patient', path: '/patient' }
    ]
  });
});

app.get('/Manage/Menus/GetMenusChildrenList', (req, res) => {
  console.log('请求子菜单列表');
  res.json({
    ...mockData,
    data: [
      { id: 1, parentId: 1, name: '今日订单', path: '/order/today' },
      { id: 2, parentId: 1, name: '历史订单', path: '/order/history' }
    ]
  });
});

app.get('/Manage/OrganStruct/GetOrganStructList', (req, res) => {
  console.log('请求组织架构列表');
  res.json({
    ...mockData,
    data: [
      { id: 1, name: '总部', level: 1, parentId: null },
      { id: 2, name: '技术部', level: 2, parentId: 1 }
    ]
  });
});

app.get('/Manage/KeyValue/GetSystemSetData', (req, res) => {
  console.log('请求系统设置数据');
  res.json({
    ...mockData,
    data: {
      systemName: '医院管理系统',
      version: '1.0.0',
      theme: 'default'
    }
  });
});

app.listen(port, () => {
  console.log(`Mock 服务器运行在 http://localhost:${port}`);
});
