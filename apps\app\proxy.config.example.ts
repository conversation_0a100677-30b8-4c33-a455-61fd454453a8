/**
 * 代理配置示例文件
 * 复制此文件为 proxy.config.ts 并修改为您的实际配置
 */

// 示例配置 - 请根据您的实际情况修改
const EXAMPLE_CONFIGS = {
  // 示例1: 单个 API 服务器
  singleServer: {
    API_BASE_URL: 'https://api.hospital.com',
    config: {
      '/api': {
        target: 'https://api.hospital.com',
        changeOrigin: true,
        ws: true,
        secure: true
      },
      '/Manage': {
        target: 'https://api.hospital.com',
        changeOrigin: true,
        ws: true,
        secure: true,
        rewrite: (path: string) => {
          console.log('代理请求:', path);
          return path;
        }
      }
    }
  },

  // 示例2: 多个 API 服务器
  multipleServers: {
    config: {
      '/api': {
        target: 'https://main-api.hospital.com',
        changeOrigin: true,
        ws: true,
        secure: true
      },
      '/Manage': {
        target: 'https://manage-api.hospital.com',
        changeOrigin: true,
        ws: true,
        secure: true,
        rewrite: (path: string) => {
          console.log('代理请求:', path);
          return path;
        }
      },
      '/Account': {
        target: 'https://auth-api.hospital.com',
        changeOrigin: true,
        ws: true,
        secure: true
      }
    }
  },

  // 示例3: 需要认证的 API
  withAuth: {
    API_BASE_URL: 'https://secure-api.hospital.com',
    config: {
      '/Manage': {
        target: 'https://secure-api.hospital.com',
        changeOrigin: true,
        ws: true,
        secure: true,
        headers: {
          'Authorization': 'Bearer your-api-token',
          'X-API-Key': 'your-api-key',
          'Content-Type': 'application/json'
        },
        rewrite: (path: string) => {
          console.log('代理请求:', path);
          return path;
        }
      }
    }
  },

  // 示例4: 路径重写
  withPathRewrite: {
    API_BASE_URL: 'https://api.hospital.com',
    config: {
      '/Manage': {
        target: 'https://api.hospital.com',
        changeOrigin: true,
        ws: true,
        secure: true,
        rewrite: (path: string) => {
          console.log('原始路径:', path);
          // 将 /Manage/PatientOrder/GetAllOrderTodayList 
          // 重写为 /api/v1/patient-orders/today
          const newPath = path
            .replace(/^\/Manage\/PatientOrder\/GetAllOrderTodayList/, '/api/v1/patient-orders/today')
            .replace(/^\/Manage\/PatientOrder\/GetAllOrderHistoryList/, '/api/v1/patient-orders/history')
            .replace(/^\/Manage\/Menus\/GetParentMenus/, '/api/v1/menus/parent')
            .replace(/^\/Manage\/Menus\/GetMenusChildrenList/, '/api/v1/menus/children')
            .replace(/^\/Manage\/OrganStruct\/GetOrganStructList/, '/api/v1/organization/list')
            .replace(/^\/Manage\/KeyValue\/GetSystemSetData/, '/api/v1/system/settings');
          
          console.log('重写后路径:', newPath);
          return newPath;
        }
      }
    }
  },

  // 示例5: 开发环境忽略 SSL
  development: {
    API_BASE_URL: 'https://dev-api.hospital.com',
    config: {
      '/Manage': {
        target: 'https://dev-api.hospital.com',
        changeOrigin: true,
        ws: true,
        secure: false, // 开发环境忽略 SSL 证书验证
        rewrite: (path: string) => {
          console.log('代理请求:', path);
          return path;
        }
      }
    }
  }
};

// 导出示例配置（实际使用时请删除此部分）
export { EXAMPLE_CONFIGS };

// 实际配置 - 请根据上面的示例选择合适的配置
const API_BASE_URL = 'https://your-actual-api-server.com';

export default {
  '/api': {
    target: API_BASE_URL,
    changeOrigin: true,
    ws: true,
    secure: true,
    headers: {
      // 如果需要认证，请取消注释
      // 'Authorization': 'Bearer your-token',
      // 'X-API-Key': 'your-api-key',
      // 'Content-Type': 'application/json'
    }
  },
  '/Manage': {
    target: API_BASE_URL,
    changeOrigin: true,
    ws: true,
    secure: true,
    rewrite: (path: string) => {
      console.log('代理请求:', path);
      // 如果需要路径重写，请在这里修改
      return path;
    },
    headers: {
      // 如果需要认证，请取消注释
      // 'Authorization': 'Bearer your-token',
      // 'X-API-Key': 'your-api-key',
      // 'Content-Type': 'application/json'
    }
  },
  '/Account': {
    target: API_BASE_URL,
    changeOrigin: true,
    ws: true,
    secure: true,
    headers: {
      // 如果需要认证，请取消注释
      // 'Authorization': 'Bearer your-token',
      // 'X-API-Key': 'your-api-key',
      // 'Content-Type': 'application/json'
    }
  }
};
