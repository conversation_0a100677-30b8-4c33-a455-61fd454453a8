/**
 * 模块类型管理 API
 */

export interface ModuleType {
  id: string;
  name: string;
  code: string;
  description?: string;
  category: string;
  status: 'active' | 'inactive';
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface ModuleTypeQuery {
  page?: number;
  pageSize?: number;
  name?: string;
  code?: string;
  category?: string;
  status?: 'active' | 'inactive';
}

export interface ModuleTypeResponse {
  success: boolean;
  data?: ModuleType[];
  total?: number;
  message?: string;
}

// VTJ 期望的类型别名
export type ModuleTypeData = ModuleType;
export type ModuleTypeListParams = ModuleTypeQuery;
export type ModuleTypeByIdParams = { id: string };
export type AddModuleTypeParams = Omit<ModuleType, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateModuleTypeParams = Partial<ModuleType>;

/**
 * 获取模块类型列表
 */
export async function fetchModuleTypeList(query: ModuleTypeQuery = {}): Promise<ModuleTypeResponse> {
  try {
    // 模拟 API 调用
    const mockModuleTypes: ModuleType[] = [
      {
        id: '1',
        name: '用户管理模块',
        code: 'USER_MANAGEMENT',
        description: '用户管理相关功能模块',
        category: '系统管理',
        status: 'active',
        sortOrder: 1,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        name: '权限管理模块',
        code: 'PERMISSION_MANAGEMENT',
        description: '权限管理相关功能模块',
        category: '系统管理',
        status: 'active',
        sortOrder: 2,
        createdAt: '2024-01-02T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z'
      },
      {
        id: '3',
        name: '数据统计模块',
        code: 'DATA_STATISTICS',
        description: '数据统计分析功能模块',
        category: '数据分析',
        status: 'active',
        sortOrder: 3,
        createdAt: '2024-01-03T00:00:00Z',
        updatedAt: '2024-01-03T00:00:00Z'
      }
    ];

    return {
      success: true,
      data: mockModuleTypes,
      total: mockModuleTypes.length
    };
  } catch (error) {
    return {
      success: false,
      message: '获取模块类型列表失败'
    };
  }
}

/**
 * 获取模块类型详情
 */
export async function getModuleTypeById(id: string): Promise<ModuleTypeResponse> {
  try {
    // 模拟 API 调用
    const mockModuleType: ModuleType = {
      id,
      name: '用户管理模块',
      code: 'USER_MANAGEMENT',
      description: '用户管理相关功能模块',
      category: '系统管理',
      status: 'active',
      sortOrder: 1,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    };

    return {
      success: true,
      data: [mockModuleType]
    };
  } catch (error) {
    return {
      success: false,
      message: '获取模块类型详情失败'
    };
  }
}

/**
 * 添加模块类型
 */
export async function addModuleType(moduleType: Omit<ModuleType, 'id' | 'createdAt' | 'updatedAt'>): Promise<ModuleTypeResponse> {
  try {
    // 模拟 API 调用
    const newModuleType: ModuleType = {
      ...moduleType,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      data: [newModuleType],
      message: '添加模块类型成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '添加模块类型失败'
    };
  }
}

/**
 * 更新模块类型
 */
export async function updateModuleType(id: string, moduleType: Partial<ModuleType>): Promise<ModuleTypeResponse> {
  try {
    // 模拟 API 调用
    const updatedModuleType: ModuleType = {
      id,
      name: moduleType.name || '用户管理模块',
      code: moduleType.code || 'USER_MANAGEMENT',
      description: moduleType.description,
      category: moduleType.category || '系统管理',
      status: moduleType.status || 'active',
      sortOrder: moduleType.sortOrder || 1,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      data: [updatedModuleType],
      message: '更新模块类型成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '更新模块类型失败'
    };
  }
}

/**
 * 删除模块类型
 */
export async function deleteModuleType(id: string): Promise<ModuleTypeResponse> {
  try {
    // 模拟 API 调用
    return {
      success: true,
      message: '删除模块类型成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '删除模块类型失败'
    };
  }
}
