# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [0.12.70](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.69...@vtj/designer@0.12.70) (2025-08-01)


### Bug Fixes

* 🐛 layout页面支持mask ([66eab47](https://gitee.com/newgateway/vtj/commits/66eab472e5b913ffd101f7c7952bd5d620f8389d))





## [0.12.69](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.68...@vtj/designer@0.12.69) (2025-08-01)


### Bug Fixes

* 🐛 修复模拟器样式 ([e5c1759](https://gitee.com/newgateway/vtj/commits/e5c1759ee8ec4c8eb76f667f612ab7fc16baa78e))
* 🐛 ai-widget-bubble style ([b69302a](https://gitee.com/newgateway/vtj/commits/b69302ad2701832b214f8dcb89eac48ed7f5533a))





## [0.12.68](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.67...@vtj/designer@0.12.68) (2025-07-31)


### Bug Fixes

* 🐛 page form style ([4255259](https://gitee.com/newgateway/vtj/commits/4255259080142319aa6e99f35d6676598adfa61b))
* 🐛 project tempalte ([bdefc48](https://gitee.com/newgateway/vtj/commits/bdefc484bf23a8828693f327f6049d1b19fe5c7c))





## [0.12.67](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.66...@vtj/designer@0.12.67) (2025-07-31)


### Bug Fixes

* 🐛 pages from style ([d604324](https://gitee.com/newgateway/vtj/commits/d604324c357accffb7f2c0040c081a33504d3655))


### Features

* ✨ 支持静态路由 ([bfcb73c](https://gitee.com/newgateway/vtj/commits/bfcb73c1625abed19d241f9ccd2b4c832ef04cca))





## [0.12.66](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.65...@vtj/designer@0.12.66) (2025-07-26)


### Bug Fixes

* 🐛 禁用发布模版的公开选项 ([7ccb698](https://gitee.com/newgateway/vtj/commits/7ccb6985ffb53e0914da1756e33c752f873fab2d))
* 🐛 设计器数据源弹窗样式 ([a488f12](https://gitee.com/newgateway/vtj/commits/a488f1232e7581a8828f509ec8455dd1c87762b7))
* 🐛 simulator body css ([c7bc28b](https://gitee.com/newgateway/vtj/commits/c7bc28b80bc2af5360ed55d062b13f2c25c49f32))





## [0.12.65](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.64...@vtj/designer@0.12.65) (2025-07-23)

**Note:** Version bump only for package @vtj/designer





## [0.12.64](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.63...@vtj/designer@0.12.64) (2025-07-23)


### Features

* ✨ 支持自定义模型 ([37ae362](https://gitee.com/newgateway/vtj/commits/37ae362786659be5bb343290ad0fbd02dbf877f1))





## [0.12.63](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.62...@vtj/designer@0.12.63) (2025-07-19)


### Bug Fixes

* 🐛 优化流输出效果 ([9ba3d5f](https://gitee.com/newgateway/vtj/commits/9ba3d5ff055ad837f4f1935a83ac59a8a4ee0f30))
* 🐛 MAX_TOKENS ([8de5dc5](https://gitee.com/newgateway/vtj/commits/8de5dc5dd333d8518a715997b65c882337a12bde))





## [0.12.62](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.61...@vtj/designer@0.12.62) (2025-07-19)

**Note:** Version bump only for package @vtj/designer





## [0.12.61](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.60...@vtj/designer@0.12.61) (2025-07-19)


### Bug Fixes

* 🐛 fix错误提示 ([8d5d7d9](https://gitee.com/newgateway/vtj/commits/8d5d7d981564247b4cef9c1ece6cfff22051a087))
* 🐛 parser 删除 @babel/core 依赖 ([76cefe2](https://gitee.com/newgateway/vtj/commits/76cefe2b95abfcce7584f81b2e66282b94f0cb3c))





## [0.12.60](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.59...@vtj/designer@0.12.60) (2025-07-18)


### Bug Fixes

* 🐛 desginer   box-sizing style ([bbeb093](https://gitee.com/newgateway/vtj/commits/bbeb09325f2ceb1100248467ca95ab59d573361b))





## [0.12.59](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.58...@vtj/designer@0.12.59) (2025-07-18)


### Bug Fixes

* 🐛 优化AI错误处理 ([2f40717](https://gitee.com/newgateway/vtj/commits/2f40717e4e1c6d086e5bd2a9ba9f11fc5c052070))





## [0.12.58](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.57...@vtj/designer@0.12.58) (2025-07-16)

**Note:** Version bump only for package @vtj/designer





## [0.12.57](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.56...@vtj/designer@0.12.57) (2025-07-16)

**Note:** Version bump only for package @vtj/designer





## [0.12.56](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.55...@vtj/designer@0.12.56) (2025-07-16)


### Features

* ✨ 代码校验并自动修复 ([205257b](https://gitee.com/newgateway/vtj/commits/205257b75b5ebcb5c02ec4ade0000388552277a6))
* ✨ parser watch ([26a942a](https://gitee.com/newgateway/vtj/commits/26a942a1b1297fa7d5c7ad49121b9175efce5b62))





## [0.12.55](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.54...@vtj/designer@0.12.55) (2025-07-09)


### Features

* ✨ AI免费额度 ([4260ac9](https://gitee.com/newgateway/vtj/commits/4260ac9a319cc0da727bd36a5e8514d629e9b442))





## [0.12.54](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.53...@vtj/designer@0.12.54) (2025-07-08)


### Bug Fixes

* 🐛 适配element-plus 2.10.3 类型 ([f55a477](https://gitee.com/newgateway/vtj/commits/f55a47700f116a67c887e9b5d93b6df92866004a))





## [0.12.53](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.52...@vtj/designer@0.12.53) (2025-07-07)


### Bug Fixes

* 🐛 优化错误提示 ([0b0ec99](https://gitee.com/newgateway/vtj/commits/0b0ec99aee9d5220358536352b7533b5b39dce1d))





## [0.12.52](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.51...@vtj/designer@0.12.52) (2025-07-07)


### Bug Fixes

* 🐛 0.12.51 ([e4d8797](https://gitee.com/newgateway/vtj/commits/e4d8797a041f6df63b7f9bba3a984f6992db1064))





## [0.12.51](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.50...@vtj/designer@0.12.51) (2025-07-07)


### Bug Fixes

* 🐛 增加AI输出超过最大限制提示错误信息 ([ea5ad47](https://gitee.com/newgateway/vtj/commits/ea5ad47efae1115c7c740f1f8c404cb368cbef6c))





## [0.12.50](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.49...@vtj/designer@0.12.50) (2025-07-04)


### Bug Fixes

* 🐛 AI偶发请求错误, 重新生成时自动应用失效问题 ([baa4b4e](https://gitee.com/newgateway/vtj/commits/baa4b4ea463e6d0b08bf54e8129206797118d91f))
* 🐛 mastergo icon ([4b31960](https://gitee.com/newgateway/vtj/commits/4b31960aa015f73febc46d0c460198a5a3741fde))





## [0.12.49](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.48...@vtj/designer@0.12.49) (2025-07-02)


### Features

* ✨ 支持mastergo ([0e1b6f3](https://gitee.com/newgateway/vtj/commits/0e1b6f398c075b0d6efc87cd79a28909e0f4a55b))





## [0.12.48](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.47...@vtj/designer@0.12.48) (2025-06-30)

**Note:** Version bump only for package @vtj/designer





## [0.12.47](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.46...@vtj/designer@0.12.47) (2025-06-24)

**Note:** Version bump only for package @vtj/designer





## [0.12.46](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.45...@vtj/designer@0.12.46) (2025-06-23)


### Bug Fixes

* 🐛 uniapp预览跳转链接 ([1d54eaa](https://gitee.com/newgateway/vtj/commits/1d54eaa31274b6b0e192d2f862fea284b71242a6))





## [0.12.45](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.44...@vtj/designer@0.12.45) (2025-06-23)


### Bug Fixes

* 🐛 下线 DevTools ([b05359c](https://gitee.com/newgateway/vtj/commits/b05359c7d2a2e3ad3ec26cca5058aaea06081163))
* 🐛 chatCompletions type ([f4779b2](https://gitee.com/newgateway/vtj/commits/f4779b21fc4c853b730862cc6c83c4f19ed0155a))





## [0.12.44](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.43...@vtj/designer@0.12.44) (2025-06-20)


### Bug Fixes

* 🐛 迁移常量定义 ([d448277](https://gitee.com/newgateway/vtj/commits/d4482775f9626178c0878d42318a89dad0d32f48))
* 🐛 AI  CompletionChunk type ([187c6cd](https://gitee.com/newgateway/vtj/commits/187c6cda0d3dd5ced313427b964b5c96bf617d18))
* 🐛 logo add pointer ([fe500b2](https://gitee.com/newgateway/vtj/commits/fe500b28710b0b28faba50d975833c3ab5c223fb))
* 🐛 openApi ([d29d618](https://gitee.com/newgateway/vtj/commits/d29d618dc071f21ac3042b6b0ba305ac68fffc55))
* 🐛 provider load project ([854b140](https://gitee.com/newgateway/vtj/commits/854b1403d7f8a62c4c9b58751f91e5fdc8cee675))





## [0.12.43](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.42...@vtj/designer@0.12.43) (2025-06-17)

**Note:** Version bump only for package @vtj/designer





## [0.12.42](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.41...@vtj/designer@0.12.42) (2025-06-17)

**Note:** Version bump only for package @vtj/designer





## [0.12.41](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.40...@vtj/designer@0.12.41) (2025-06-16)


### Features

* ✨ SizeSetter支持滚轮输入 ([e2cceb3](https://gitee.com/newgateway/vtj/commits/e2cceb354934ba74186ecde03436e491966aa71e))





## [0.12.40](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.39...@vtj/designer@0.12.40) (2025-06-12)


### Bug Fixes

* 🐛 根据条件自动判断开启数据配置项 ([f7f1f1f](https://gitee.com/newgateway/vtj/commits/f7f1f1f5fddabe3f014de10de6d3843676fab232))
* 🐛 可拖拽不在组件列表的不同html元素 ([cb3c2c7](https://gitee.com/newgateway/vtj/commits/cb3c2c77e16b4e94e96a87b8653ed21dd2860040))
* 🐛 ai对话错误时取消请求 ([15d3193](https://gitee.com/newgateway/vtj/commits/15d319366144d094d35eb027ed399ea651763af9))
* 🐛 inject增加默认值null ([2e0e638](https://gitee.com/newgateway/vtj/commits/2e0e638f049203f8ed389aae46c8248a774f2907))


### Features

* ✨ 页面引入的区块支持快捷方式打开 ([4cc9288](https://gitee.com/newgateway/vtj/commits/4cc92889245c2e32993cf00d6ceb8c26dec6c7e7))
* ✨ report collect project uid ([8fa2a88](https://gitee.com/newgateway/vtj/commits/8fa2a884d883883cc9d6fd9bbbfd3579882d58a2))





## [0.12.39](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.38...@vtj/designer@0.12.39) (2025-06-11)


### Bug Fixes

* 🐛 更改区块同步更新页面引用区块 ([962f971](https://gitee.com/newgateway/vtj/commits/962f9714c953dba15367717a2857b5dcbdba89c5))





## [0.12.38](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.37...@vtj/designer@0.12.38) (2025-06-11)


### Bug Fixes

* 🐛 区块首次拖进页面无法再拖拽放置问题 ([65df49a](https://gitee.com/newgateway/vtj/commits/65df49a277267913f4dd8e291bf14b9e095b9eaa))





## [0.12.37](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.36...@vtj/designer@0.12.37) (2025-06-09)


### Bug Fixes

* 🐛 新增页面表单样式 ([17b6135](https://gitee.com/newgateway/vtj/commits/17b6135bc01076e21433c6fdf3b3762934a829af))





## [0.12.36](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.35...@vtj/designer@0.12.36) (2025-06-09)


### Bug Fixes

* 🐛 normalizedStyle ([1efe6ad](https://gitee.com/newgateway/vtj/commits/1efe6adcef2859acfb7733659cc8329587505342))


### Features

* ✨ RouterView ([112b021](https://gitee.com/newgateway/vtj/commits/112b0217ea408074cd6eeda0ae1e8e1f8206498e))





## [0.12.35](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.34...@vtj/designer@0.12.35) (2025-06-06)

**Note:** Version bump only for package @vtj/designer





## [0.12.34](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.33...@vtj/designer@0.12.34) (2025-06-06)


### Features

* ✨ 支持设置物料是否显示到组件库面板, utils 和 icons 依赖改为可选 ([747fda3](https://gitee.com/newgateway/vtj/commits/747fda31e72b211d9dff63953f4c095ea41d7368))





## [0.12.33](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.32...@vtj/designer@0.12.33) (2025-06-04)


### Bug Fixes

* 🐛 图片/json识别加入到token统计 ([96063c8](https://gitee.com/newgateway/vtj/commits/96063c847a20a1cd6af3f0047388740076b91ee3))





## [0.12.32](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.31...@vtj/designer@0.12.32) (2025-06-03)


### Features

* ✨ AI支持sketch figma 元数据文件识别 ([77adea0](https://gitee.com/newgateway/vtj/commits/77adea04e80aa0d4b100eececb311a9dffaae222))





## [0.12.31](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.30...@vtj/designer@0.12.31) (2025-05-29)


### Bug Fixes

* 🐛 remote配置错误 ([3d36b53](https://gitee.com/newgateway/vtj/commits/3d36b53b5602dc881409dd257fbd94925ed17016))
* 🐛 uniapp 平台没有区分页面和区块的生命周期下拉选项 ([2080000](https://gitee.com/newgateway/vtj/commits/20800007f3f315e33a0dd6ec0e78aeb38fc0f252))


### Features

* ✨ engine state ([b238b25](https://gitee.com/newgateway/vtj/commits/b238b25fb811db6c9d00ea3c6b5073fa88a139d7))





## [0.12.30](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.29...@vtj/designer@0.12.30) (2025-05-27)


### Bug Fixes

* 🐛 更换登录地址 ([ccac53e](https://gitee.com/newgateway/vtj/commits/ccac53ed6367543992322afcd90c90130695949d))
* 🐛 支付提示 ([15dfff3](https://gitee.com/newgateway/vtj/commits/15dfff35e55a4c50774ec93d403557a432179386))





## [0.12.29](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.28...@vtj/designer@0.12.29) (2025-05-23)

**Note:** Version bump only for package @vtj/designer





## [0.12.28](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.27...@vtj/designer@0.12.28) (2025-05-23)

**Note:** Version bump only for package @vtj/designer





## [0.12.27](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.26...@vtj/designer@0.12.27) (2025-05-23)

**Note:** Version bump only for package @vtj/designer





## [0.12.26](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.25...@vtj/designer@0.12.26) (2025-05-23)


### Bug Fixes

* 🐛 ai dsl error ([5b1ae66](https://gitee.com/newgateway/vtj/commits/5b1ae66f0ba5215bf677e0a3cc4fdeac3101c6a3))





## [0.12.25](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.24...@vtj/designer@0.12.25) (2025-05-22)


### Bug Fixes

* 🐛 模版面板滚动条样式 ([18adea0](https://gitee.com/newgateway/vtj/commits/18adea0392011e470bd5fd67cc781330df57c0d3))
* 🐛 模版面板折叠失效 ([bc40c6a](https://gitee.com/newgateway/vtj/commits/bc40c6a2e165a328571656d898ea3d52c5999cc8))





## [0.12.24](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.23...@vtj/designer@0.12.24) (2025-05-21)

**Note:** Version bump only for package @vtj/designer





## [0.12.23](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.22...@vtj/designer@0.12.23) (2025-05-21)


### Bug Fixes

* 🐛 改小新增页面弹窗高度 ([71e99a3](https://gitee.com/newgateway/vtj/commits/71e99a352c3de9ad690547939bbf89a818473986))
* 🐛 优化提示词 ([04472da](https://gitee.com/newgateway/vtj/commits/04472dacc78aea5606ab62c795d359ca2edb2ddc))





## [0.12.22](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.21...@vtj/designer@0.12.22) (2025-05-20)

**Note:** Version bump only for package @vtj/designer





## [0.12.21](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.20...@vtj/designer@0.12.21) (2025-05-16)


### Bug Fixes

* 🐛 actions alert ([da525fa](https://gitee.com/newgateway/vtj/commits/da525fa4a0e4d450ed490b841c533263afe08d08))
* 🐛 renderer injects 失效问题 ([5f50c7e](https://gitee.com/newgateway/vtj/commits/5f50c7e1e7fabd59b36571dad2fb076417283341))





## [0.12.20](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.19...@vtj/designer@0.12.20) (2025-05-15)


### Bug Fixes

* 🐛 锁定增加提示 ([41ca24d](https://gitee.com/newgateway/vtj/commits/41ca24dc1ac995347931f17aff5061a4dc4239de))


### Features

* ✨ 支持项目锁定 ([5aa88ed](https://gitee.com/newgateway/vtj/commits/5aa88edfc37caa9ea5c61e5d54636876444a631c))
* ✨ 支持应用增强配置 ([7abb434](https://gitee.com/newgateway/vtj/commits/7abb4349377ef268d6ff7e92691384c0d7980214))





## [0.12.19](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.18...@vtj/designer@0.12.19) (2025-05-12)

**Note:** Version bump only for package @vtj/designer





## [0.12.18](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.17...@vtj/designer@0.12.18) (2025-05-12)

**Note:** Version bump only for package @vtj/designer





## [0.12.17](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.16...@vtj/designer@0.12.17) (2025-05-12)


### Bug Fixes

* 🐛 升级 element-plus 导致api管理面板折叠失效问题 ([130fa7b](https://gitee.com/newgateway/vtj/commits/130fa7b6399ff366f8a9e64fa240d3137391d446))





## [0.12.16](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.15...@vtj/designer@0.12.16) (2025-05-12)

**Note:** Version bump only for package @vtj/designer





## [0.12.15](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.14...@vtj/designer@0.12.15) (2025-05-07)


### Bug Fixes

* 🐛 AI图片上传样式 ([cea81a4](https://gitee.com/newgateway/vtj/commits/cea81a466f8146aae165055010ca5a7b7166151b))
* 🐛 parser错误提示 ([0ad49d6](https://gitee.com/newgateway/vtj/commits/0ad49d6e034f8e68949e1774d72a4610e799cc67))





## [0.12.14](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.13...@vtj/designer@0.12.14) (2025-05-07)

**Note:** Version bump only for package @vtj/designer





## [0.12.13](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.12...@vtj/designer@0.12.13) (2025-05-06)

**Note:** Version bump only for package @vtj/designer





## [0.12.12](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.11...@vtj/designer@0.12.12) (2025-05-06)


### Features

* ✨ 支持图标组件物料 ([62a22fd](https://gitee.com/newgateway/vtj/commits/62a22fd139a70107668bac46a025797855e36703))





## [0.12.11](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.10...@vtj/designer@0.12.11) (2025-05-05)


### Features

* ✨ 源码视图支持编辑 ([8d794df](https://gitee.com/newgateway/vtj/commits/8d794df79f76e919a566d55310ff533c63a844b3))
* ✨ 支持v-else-if 和 v-else 代码分支 ([6965137](https://gitee.com/newgateway/vtj/commits/69651374b83bffd4bb65f4e8d7de82eb4dc87e7d))





## [0.12.10](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.9...@vtj/designer@0.12.10) (2025-04-30)


### Bug Fixes

* 🐛 对话取消 ([e7737df](https://gitee.com/newgateway/vtj/commits/e7737dfd1074bbfa3674ea7000e07bbeafa74588))
* 🐛 检查AI是否有省略代码 ([3852e4b](https://gitee.com/newgateway/vtj/commits/3852e4b3725593423a99179804fb84573f1bbe69))


### Features

* ✨ 发布模版支持更换截图 ([f570adc](https://gitee.com/newgateway/vtj/commits/f570adcd1c04bee9bd33ff743b835dbf698bd162))
* ✨ 支持取消AI对话 ([87addc7](https://gitee.com/newgateway/vtj/commits/87addc7512c33a1738a5808a12f9d9367fb2e260))





## [0.12.9](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.8...@vtj/designer@0.12.9) (2025-04-29)


### Bug Fixes

* 🐛 响应事件bug ([ef4db67](https://gitee.com/newgateway/vtj/commits/ef4db6735258d4d1e87749412046661090b28ba1))





## [0.12.8](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.7...@vtj/designer@0.12.8) (2025-04-29)


### Features

* ✨ AI 图生代码 ([dd7fee5](https://gitee.com/newgateway/vtj/commits/dd7fee5c281ed9fce05cad14ae8ec2c37b9f0b0c))





## [0.12.7](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.6...@vtj/designer@0.12.7) (2025-04-27)

**Note:** Version bump only for package @vtj/designer





## [0.12.6](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.5...@vtj/designer@0.12.6) (2025-04-25)


### Bug Fixes

* 🐛 文本和表达式合成 ([6452a85](https://gitee.com/newgateway/vtj/commits/6452a859baa6e65c6ca8de40f93d432f2e7fa7c8))
* 🐛 completions error ([14fc87c](https://gitee.com/newgateway/vtj/commits/14fc87c76dbec059330285b9d83a4d3bdacae441))
* 🐛 saveChat 异常处理 ([a3f4565](https://gitee.com/newgateway/vtj/commits/a3f45656bb5cd2fbf32a79de97415751b32516bd))
* 🐛 updateChatDsl异常处理 ([7ea5bc8](https://gitee.com/newgateway/vtj/commits/7ea5bc8ff5afa135643998672ce82ee1b47134eb))





## [0.12.5](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.4...@vtj/designer@0.12.5) (2025-04-25)


### Bug Fixes

* 🐛 设计器接入指南 ([bd08152](https://gitee.com/newgateway/vtj/commits/bd081521d049176dfc334305a7829780363b0d81))
* 🐛 优化样式 ([62895ec](https://gitee.com/newgateway/vtj/commits/62895ecc2f7bf11e342b7ae26ecb2d747263d1ea))
* 🐛 vue2Dsl未处理异常 ([f323395](https://gitee.com/newgateway/vtj/commits/f3233951ae218817a6434f86a853039fe838532e))





## [0.12.4](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.3...@vtj/designer@0.12.4) (2025-04-23)

**Note:** Version bump only for package @vtj/designer





## [0.12.3](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.2...@vtj/designer@0.12.3) (2025-04-23)


### Bug Fixes

* 🐛 修复parser已知问题 ([c86b738](https://gitee.com/newgateway/vtj/commits/c86b738a2ed95eb02c1f4070c4b2a57c247d36e1))


### Features

* ✨ report timer ([2113599](https://gitee.com/newgateway/vtj/commits/2113599a2cb649fe34bf42d57177b7ae2aa1b8de))





## [0.12.2](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.1...@vtj/designer@0.12.2) (2025-04-22)


### Bug Fixes

* 🐛 修复chat dsl保存了错误信息问题 ([07120ce](https://gitee.com/newgateway/vtj/commits/07120ceb8697e9e3ef6c9af5443ffdfe391d4dc7))





## [0.12.1](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.0...@vtj/designer@0.12.1) (2025-04-22)


### Bug Fixes

* 🐛 修复样式 ([b20f724](https://gitee.com/newgateway/vtj/commits/b20f7243d77ddcab70ee2fbdc4de93b33d99f22f))





# [0.12.0](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.0-alpha.2...@vtj/designer@0.12.0) (2025-04-22)

**Note:** Version bump only for package @vtj/designer





# [0.12.0-alpha.2](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.0-alpha.1...@vtj/designer@0.12.0-alpha.2) (2025-04-22)


### Bug Fixes

* 🐛 ai style ([1e4e3a4](https://gitee.com/newgateway/vtj/commits/1e4e3a41341a47677264745a8e9a3a52902a411d))





# [0.12.0-alpha.1](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.12.0-alpha.0...@vtj/designer@0.12.0-alpha.1) (2025-04-22)


### Features

* ✨ openapi ([d38d478](https://gitee.com/newgateway/vtj/commits/d38d478b5e82b1648363c139112ea954dfb511ca))





# [0.12.0-alpha.0](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.16...@vtj/designer@0.12.0-alpha.0) (2025-04-21)


### Bug Fixes

* 🐛 parseVue ([1f40a4c](https://gitee.com/newgateway/vtj/commits/1f40a4cce6f9849126b417611c59438a06ab47a2))
* 🐛 replacer ([2811a20](https://gitee.com/newgateway/vtj/commits/2811a20110e14129fe29722cca15e9e5ea16cf08))


### Features

* ✨ 节点/事件解析 ([27670e2](https://gitee.com/newgateway/vtj/commits/27670e2e7d52c1d76a2f278819b0d4beefa8e735))
* ✨ 支持用签名自动授权登录 ([cd273e4](https://gitee.com/newgateway/vtj/commits/cd273e415eb4a5dadf741e8cd1d25505ab5fedfa))
* ✨ AI 对话ui ([53b8461](https://gitee.com/newgateway/vtj/commits/53b8461d0511e85a2d37bc85128163113f2d22d9))
* ✨ ai ui ([0c19328](https://gitee.com/newgateway/vtj/commits/0c1932841926f5ce643bfce4725a786d5a46a34b))
* ✨ AI Widget ([e758a1b](https://gitee.com/newgateway/vtj/commits/e758a1b1e00496a20444d42c427288984a5766a5))
* ✨ AI助手 ([72771d2](https://gitee.com/newgateway/vtj/commits/72771d20cf2a83b644adc776814c160758475504))
* ✨ AI助手 ([702d912](https://gitee.com/newgateway/vtj/commits/702d91255d2860ee899f06f598d8043e6db9620d))
* ✨ openapi ([8a0f413](https://gitee.com/newgateway/vtj/commits/8a0f413d3edc0ba6117ade47a871c69ac40cfe39))





## [0.11.16](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.15...@vtj/designer@0.11.16) (2025-04-10)


### Bug Fixes

* 🐛 block文件更新没触发dsl更新 ([f5ab5e2](https://gitee.com/newgateway/vtj/commits/f5ab5e2c8bebd14381bcbaf84dc9a896fc676b4e))





## [0.11.15](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.14...@vtj/designer@0.11.15) (2025-04-09)


### Bug Fixes

* 🐛 优化api管理列表展示 ([6f25806](https://gitee.com/newgateway/vtj/commits/6f25806e81a7a6cecc88c5e42a3fa7a22ce7836a))
* 🐛 report add isVtjUrl ([e10fc5c](https://gitee.com/newgateway/vtj/commits/e10fc5c065342e8051c538defdfee3fe6bc69d57))





## [0.11.14](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.13...@vtj/designer@0.11.14) (2025-04-07)


### Bug Fixes

* 🐛 修复组件库搜索结果重复问题 ([1ddac34](https://gitee.com/newgateway/vtj/commits/1ddac3476a8e1428b47ce1ca5c14f1becd7fb308))





## [0.11.13](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.12...@vtj/designer@0.11.13) (2025-04-02)


### Bug Fixes

* 🐛 不采集非vtj请求的错误信息 ([61ce992](https://gitee.com/newgateway/vtj/commits/61ce9922cd7bae5fb4aed7fd50fd386629b4aedd))
* 🐛 report useResponse return Promise.reject ([416c338](https://gitee.com/newgateway/vtj/commits/416c3383751ef27387ffa3603c4e0b928ffaaa93))





## [0.11.12](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.11...@vtj/designer@0.11.12) (2025-03-28)


### Bug Fixes

* 🐛 Failed to read the 'state' property from 'History' ([74a60bb](https://gitee.com/newgateway/vtj/commits/74a60bbd54e014cf81da1c8687bfe0b4bc38f97a))
* 🐛 ResizeObserver loop completed with undelivered notificat ([4c194a8](https://gitee.com/newgateway/vtj/commits/4c194a88e96a83075de7b629ffa708ce514dacf6))





## [0.11.11](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.10...@vtj/designer@0.11.11) (2025-03-28)

**Note:** Version bump only for package @vtj/designer





## [0.11.10](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.9...@vtj/designer@0.11.10) (2025-03-27)


### Bug Fixes

* 🐛 report error ([bba69ed](https://gitee.com/newgateway/vtj/commits/bba69ed4ea1c02e04bdc0405ab1d3d910015abc0))





## [0.11.9](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.8...@vtj/designer@0.11.9) (2025-03-27)


### Bug Fixes

* 🐛 report error message is null ([663fd1c](https://gitee.com/newgateway/vtj/commits/663fd1c0c00b82d449abfd71413a52303b598d6e))





## [0.11.8](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.7...@vtj/designer@0.11.8) (2025-03-27)


### Features

* ✨ report 模块,修复access和style出码bug ([db401ee](https://gitee.com/newgateway/vtj/commits/db401ee22a2c2fb85f4867755047966164b8e9f8))





## [0.11.7](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.6...@vtj/designer@0.11.7) (2025-03-25)


### Bug Fixes

* 🐛 样式设置面板滚动条丢失 ([130ee15](https://gitee.com/newgateway/vtj/commits/130ee15a792eecfcce05ca8957772287707c9d6d))
* 🐛 style expression binder ([b6965fb](https://gitee.com/newgateway/vtj/commits/b6965fb7b04df522fe4651fb75a604b2064bd877))





## [0.11.6](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.5...@vtj/designer@0.11.6) (2025-03-21)


### Bug Fixes

* 🐛 更换自定义视图图标 ([0e11709](https://gitee.com/newgateway/vtj/commits/0e1170980a7f404c76853d3da24772dd2d6972e4))
* 🐛 remote改为常量 ([84fc4e9](https://gitee.com/newgateway/vtj/commits/84fc4e9634045ca69206d1facdbfffd8c17e73cb))





## [0.11.5](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.4...@vtj/designer@0.11.5) (2025-03-20)

**Note:** Version bump only for package @vtj/designer





## [0.11.4](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.3...@vtj/designer@0.11.4) (2025-03-19)

**Note:** Version bump only for package @vtj/designer





## [0.11.3](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.2...@vtj/designer@0.11.3) (2025-03-19)


### Bug Fixes

* 🐛 增加默认access ([4871251](https://gitee.com/newgateway/vtj/commits/4871251e7a79566f2d6a499f40f26f197f38d2dc))
* 🐛 remote config url ([e663bfd](https://gitee.com/newgateway/vtj/commits/e663bfdd254641dbc9a088e34ee5b4df2dc091eb))





## [0.11.2](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.1...@vtj/designer@0.11.2) (2025-03-19)


### Features

* ✨ style支持变量绑定 ([3b81457](https://gitee.com/newgateway/vtj/commits/3b81457159ce88eb09a5286800d3c302bb7fe28d))





## [0.11.1](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.1-alpha.1...@vtj/designer@0.11.1) (2025-03-18)

**Note:** Version bump only for package @vtj/designer





## [0.11.1-alpha.1](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.1-alpha.0...@vtj/designer@0.11.1-alpha.1) (2025-03-18)

**Note:** Version bump only for package @vtj/designer





## [0.11.1-alpha.0](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.11.0...@vtj/designer@0.11.1-alpha.0) (2025-03-18)

**Note:** Version bump only for package @vtj/designer





# [0.11.0](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.15...@vtj/designer@0.11.0) (2025-03-17)

**Note:** Version bump only for package @vtj/designer





## [0.10.15](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.14...@vtj/designer@0.10.15) (2025-03-14)


### Bug Fixes

* 🐛 解决设计器request被污染的问题 ([338a3ba](https://gitee.com/newgateway/vtj/commits/338a3baedbb21ae41c489b4932b9004da80c36cc))





## [0.10.14](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.13...@vtj/designer@0.10.14) (2025-03-14)


### Bug Fixes

* 🐛 normalizedStyle ([b649f10](https://gitee.com/newgateway/vtj/commits/b649f109af7b11f29fcc51b523185c3302706d9a))





## [0.10.13](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.12...@vtj/designer@0.10.13) (2025-03-13)


### Features

* ✨ 物料开发示例工程 ([936fc1f](https://gitee.com/newgateway/vtj/commits/936fc1fe6afcd22cee0c988a88cba389bd583439))





## [0.10.12](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.11...@vtj/designer@0.10.12) (2025-03-11)

**Note:** Version bump only for package @vtj/designer





## [0.10.11](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.10...@vtj/designer@0.10.11) (2025-03-11)

**Note:** Version bump only for package @vtj/designer





## [0.10.10](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.9...@vtj/designer@0.10.10) (2025-03-07)


### Bug Fixes

* 🐛 html2canvas useCORS ([88d9244](https://gitee.com/newgateway/vtj/commits/88d9244f4ff4365b4b8c5b9c4db60fb10c91b1ea))
* 🐛 v-item 内容过长导致的样式问题 ([1a5d378](https://gitee.com/newgateway/vtj/commits/1a5d378677e767289f1f91ee720881b86c2825da))





## [0.10.9](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.8...@vtj/designer@0.10.9) (2025-03-04)

**Note:** Version bump only for package @vtj/designer





## [0.10.8](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.7...@vtj/designer@0.10.8) (2025-03-04)

**Note:** Version bump only for package @vtj/designer





## [0.10.7](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.5...@vtj/designer@0.10.7) (2025-03-04)


### Bug Fixes

* 🐛 关闭了辅助线。在页面切换之后，辅助线还是会显示出来 ([c436f6d](https://gitee.com/newgateway/vtj/commits/c436f6d0e5df42b957d00d8534983d3844301aab))
* 🐛 组件库筛选结果出现重复 ([564c11c](https://gitee.com/newgateway/vtj/commits/564c11c0c1c1cf7ace9bd7f395b7acce242b8fd8))





## [0.10.6](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.5...@vtj/designer@0.10.6) (2025-02-28)


### Bug Fixes

* 🐛 关闭了辅助线。在页面切换之后，辅助线还是会显示出来 ([c436f6d](https://gitee.com/newgateway/vtj/commits/c436f6d0e5df42b957d00d8534983d3844301aab))





## [0.10.5](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.4...@vtj/designer@0.10.5) (2025-02-27)

**Note:** Version bump only for package @vtj/designer





## [0.10.4](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.3...@vtj/designer@0.10.4) (2025-02-26)

**Note:** Version bump only for package @vtj/designer





## [0.10.3](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.2...@vtj/designer@0.10.3) (2025-02-26)


### Bug Fixes

* 🐛 调整uniapp本地文件存储目录 ([fcee7aa](https://gitee.com/newgateway/vtj/commits/fcee7aa537d68dfe9f9a0a03f9516c64810462b7))





## [0.10.2](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.1...@vtj/designer@0.10.2) (2025-02-25)

**Note:** Version bump only for package @vtj/designer





## [0.10.1](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.1-alpha.7...@vtj/designer@0.10.1) (2025-02-25)

**Note:** Version bump only for package @vtj/designer





## [0.10.1-alpha.7](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.1-alpha.6...@vtj/designer@0.10.1-alpha.7) (2025-02-25)


### Bug Fixes

* 🐛 uni-ui ([eb90a85](https://gitee.com/newgateway/vtj/commits/eb90a85ed74658162ddf1c08eca746663ee7793a))


### Features

* ✨ template 支持按platfrom查询 ([d48f7a6](https://gitee.com/newgateway/vtj/commits/d48f7a6274bfd4def7f7ef5d3575bdd46d4c8cee))





## [0.10.1-alpha.6](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.1-alpha.5...@vtj/designer@0.10.1-alpha.6) (2025-02-24)

**Note:** Version bump only for package @vtj/designer





## [0.10.1-alpha.5](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.1-alpha.4...@vtj/designer@0.10.1-alpha.5) (2025-02-24)

**Note:** Version bump only for package @vtj/designer





## [0.10.1-alpha.4](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.1-alpha.3...@vtj/designer@0.10.1-alpha.4) (2025-02-24)


### Bug Fixes

* 🐛 uniapp mock ([1cf64a8](https://gitee.com/newgateway/vtj/commits/1cf64a88b69336c59db9233dbc24bf31ccdc4ecd))





## [0.10.1-alpha.3](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.1-alpha.2...@vtj/designer@0.10.1-alpha.3) (2025-02-22)


### Features

* ✨ engine add pageRouteName option ([9c5aa80](https://gitee.com/newgateway/vtj/commits/9c5aa80b67bcd7127c9f5c569be49b00069b13b5))





## [0.10.1-alpha.2](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.1-alpha.1...@vtj/designer@0.10.1-alpha.2) (2025-02-22)


### Bug Fixes

* 🐛 修复TS报错 ([e91fd22](https://gitee.com/newgateway/vtj/commits/e91fd2207c9e5fce9d70aff53aa960731c28d6fa))
* 🐛 components group tab ([48b6529](https://gitee.com/newgateway/vtj/commits/48b65297ca1b78c6beac588bee036cac11444e66))
* 🐛 ide access auth ([f259285](https://gitee.com/newgateway/vtj/commits/f25928584898144848f2781e6876a49b7a44c0d6))


### Features

* ✨ 模板管理添加tab ([8b0d71a](https://gitee.com/newgateway/vtj/commits/8b0d71ae78a24ce7d4f209a9665c11ace53bc668))
* ✨ mock改为依赖控制开启 ([6d01e1a](https://gitee.com/newgateway/vtj/commits/6d01e1aa59c7a6b2b7127b136fd8fdd829e5f976))





## [0.10.1-alpha.1](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.1-alpha.0...@vtj/designer@0.10.1-alpha.1) (2025-02-20)


### Features

* ✨ uni-ui 物料 ([a959e45](https://gitee.com/newgateway/vtj/commits/a959e4528930d3b9aa3cd8b2bbeb89c2527d9be2))
* ✨ uniapp 支持 uni-ui ([1ee244c](https://gitee.com/newgateway/vtj/commits/1ee244cd04b300c2bd7e84579ea01e4ec98169b8))





## [0.10.1-alpha.0](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.10.0...@vtj/designer@0.10.1-alpha.0) (2025-02-19)


### Bug Fixes

* 🐛 merge dependencies ([a014620](https://gitee.com/newgateway/vtj/commits/a014620aea23a0bcf8a5dca306dc6f799ef8396e))





# [0.10.0](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.30...@vtj/designer@0.10.0) (2025-02-19)


### Bug Fixes

* 🐛 uniapp coder ([960bc99](https://gitee.com/newgateway/vtj/commits/960bc99553045131ce609293a66c799918e3c5bb))
* 🐛 uniapp saveProject update pages.json ([99844ad](https://gitee.com/newgateway/vtj/commits/99844ad6fd285c7424dd56eb7006e496cec09d92))
* 🐛 uniapp tabBar ([45a05d4](https://gitee.com/newgateway/vtj/commits/45a05d49d53645aaddb7a841b23b961e5337c3f9))


### Features

* ✨ 重构设计器刷新功能 ([e028015](https://gitee.com/newgateway/vtj/commits/e028015e192c34b4ef5013418509126b2e7c36a9))
* ✨ local init uniapp project config ([63c665a](https://gitee.com/newgateway/vtj/commits/63c665a120fdfb5ccfd9df73356ec4d87dd001a3))
* ✨ temp save ([0dd3c2e](https://gitee.com/newgateway/vtj/commits/0dd3c2e3fabf5f490ea3d01353cd9f185cce0455))
* ✨ uni deps ([3c05124](https://gitee.com/newgateway/vtj/commits/3c0512437948a3d640503d15c90b83a471708ca2))
* ✨ uni生命周期 ([b2e1f6a](https://gitee.com/newgateway/vtj/commits/b2e1f6a65d6e5a093f6836ce6b91759dcea73137))
* ✨ uniapp 页面预览 ([16075f6](https://gitee.com/newgateway/vtj/commits/16075f6469f2e5790fe73ed7263b855fa8e4dd1d))
* ✨ uniapp 预览工程 ([262e4e2](https://gitee.com/newgateway/vtj/commits/262e4e2080bd46945655148645eebb91f441b590))
* ✨ uniapp 支持全局css ([18fb654](https://gitee.com/newgateway/vtj/commits/18fb654e13691b7226b77b6b93379b876d2089a2))
* ✨ uniapp coder ([5e961d2](https://gitee.com/newgateway/vtj/commits/5e961d2154bcce1bfefef8d054878df13aa706c8))
* ✨ uniapp page setting ([4946b16](https://gitee.com/newgateway/vtj/commits/4946b16a374781dd7af9c8aad8f64062c9e5425d))
* ✨ uniapp uniconfig 协议 ([7f5790b](https://gitee.com/newgateway/vtj/commits/7f5790be80d840ef0735a45034b7edcd4265c76c))





## [0.9.30](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.29...@vtj/designer@0.9.30) (2025-02-18)

**Note:** Version bump only for package @vtj/designer





## [0.9.29](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.28...@vtj/designer@0.9.29) (2025-02-13)


### Bug Fixes

* 🐛 根据 platform 动态设置pure默认值 ([109c3cf](https://gitee.com/newgateway/vtj/commits/109c3cf5e4191fbcdba866376791fd9fb363a71d))
* 🐛 还原页面默认pure为true ([fbb94a4](https://gitee.com/newgateway/vtj/commits/fbb94a45fe87ed58f0a6335fb91bc7496463ed10))





## [0.9.28](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.27...@vtj/designer@0.9.28) (2025-02-13)


### Bug Fixes

* 🐛 新建页面Pure默认为false ([907037a](https://gitee.com/newgateway/vtj/commits/907037a157f09be0048fc9110ea734e5a5c6e550))





## [0.9.27](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.26...@vtj/designer@0.9.27) (2025-02-12)


### Bug Fixes

* 🐛 去掉XIcon引入 ([20036f4](https://gitee.com/newgateway/vtj/commits/20036f48fd85108914c46dfdf08547871d32477f))
* 🐛 使用HTML标签显示图标;颜色样式使用变量 ([4324b8f](https://gitee.com/newgateway/vtj/commits/4324b8fb71bb971708ff50dd93edb415c399fb0c))
* 🐛 优化变量绑定器样式 ([9dc4d49](https://gitee.com/newgateway/vtj/commits/9dc4d493b1c65a9fde56ef8dc26c50afd2a25963))


### Features

* ✨ 变量绑定器高级选项卡的内容支持搜索功能 ([1dddb54](https://gitee.com/newgateway/vtj/commits/1dddb5466b87932ced2d133ad8fa43fdc9dc8000))
* ✨ 添加vanIconSetter ([cd5a97e](https://gitee.com/newgateway/vtj/commits/cd5a97ee1764af042de459ecabf90a828c2e3d55))





## [0.9.26](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.25...@vtj/designer@0.9.26) (2025-02-11)

**Note:** Version bump only for package @vtj/designer





## [0.9.25](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.24...@vtj/designer@0.9.25) (2025-01-23)


### Bug Fixes

* 🐛 被引用的区块渲染时总是刷新的问题导致的报错问题 ([2258a55](https://gitee.com/newgateway/vtj/commits/2258a5521f10d3a2988ed1594dc0fd15d6b28838))





## [0.9.24](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.23...@vtj/designer@0.9.24) (2025-01-22)


### Features

* ✨ 版本更新提示 ([570c449](https://gitee.com/newgateway/vtj/commits/570c449d2629ef6a5b79ac801c24ccb3b47364cf))





## [0.9.23](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.22...@vtj/designer@0.9.23) (2025-01-22)

**Note:** Version bump only for package @vtj/designer





## [0.9.22](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.21...@vtj/designer@0.9.22) (2025-01-16)

**Note:** Version bump only for package @vtj/designer





## [0.9.21](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.20...@vtj/designer@0.9.21) (2025-01-14)

**Note:** Version bump only for package @vtj/designer





## [0.9.20](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.19...@vtj/designer@0.9.20) (2025-01-14)

**Note:** Version bump only for package @vtj/designer





## [0.9.19](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.18...@vtj/designer@0.9.19) (2025-01-14)

**Note:** Version bump only for package @vtj/designer





## [0.9.18](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.17...@vtj/designer@0.9.18) (2025-01-14)

**Note:** Version bump only for package @vtj/designer





## [0.9.17](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.16...@vtj/designer@0.9.17) (2025-01-14)

**Note:** Version bump only for package @vtj/designer





## [0.9.16](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.15...@vtj/designer@0.9.16) (2025-01-13)


### Features

* ✨ h5平台 ([0497ccb](https://gitee.com/newgateway/vtj/commits/0497ccbb53f01a537176cae36f69b5d3019fc68e))
* ✨ H5平台协议 ([51484a8](https://gitee.com/newgateway/vtj/commits/51484a8d0723897e462e1e2c37e16dbdf2546bea))





## [0.9.15](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.14...@vtj/designer@0.9.15) (2025-01-13)


### Bug Fixes

* 🐛 text node getBoundingClientRect is not a function ([a80cc00](https://gitee.com/newgateway/vtj/commits/a80cc00b1d8369c34218c4890320a94a191e70e0))





## [0.9.14](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.13...@vtj/designer@0.9.14) (2025-01-10)


### Bug Fixes

* 🐛 设置器值数据类型匹配错误问题 ([ee37d88](https://gitee.com/newgateway/vtj/commits/ee37d8802e3412b4a68aea5140f895ca4812ce51))





## [0.9.13](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.12...@vtj/designer@0.9.13) (2025-01-10)


### Features

* ✨ 优化辅助线渲染 ([d8e66f2](https://gitee.com/newgateway/vtj/commits/d8e66f2842b6017a8bb0e44dd4d4c66774c9a63a))





## [0.9.12](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.11...@vtj/designer@0.9.12) (2025-01-10)


### Features

* ✨ 增加辅助线 ([eda8618](https://gitee.com/newgateway/vtj/commits/eda861845db182e0d270b24e9ed6a0835fcc5bee))





## [0.9.11](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.10...@vtj/designer@0.9.11) (2025-01-09)

**Note:** Version bump only for package @vtj/designer





## [0.9.10](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.9...@vtj/designer@0.9.10) (2025-01-09)

**Note:** Version bump only for package @vtj/designer





## [0.9.9](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.8...@vtj/designer@0.9.9) (2025-01-08)

**Note:** Version bump only for package @vtj/designer





## [0.9.8](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.7...@vtj/designer@0.9.8) (2025-01-06)


### Features

* ✨ @vtj/renderer 与 element-plus 解耦 ([6e63fe8](https://gitee.com/newgateway/vtj/commits/6e63fe81bf5e7047f1c621277b9104d2b44b02bd))





## [0.9.7](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.6...@vtj/designer@0.9.7) (2025-01-04)


### Bug Fixes

* 🐛 优化插槽提示 ([9aa2ea4](https://gitee.com/newgateway/vtj/commits/9aa2ea459a023655450e39b7eadf99095ca093b5))





## [0.9.6](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.5...@vtj/designer@0.9.6) (2025-01-03)

**Note:** Version bump only for package @vtj/designer





## [0.9.5](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.4...@vtj/designer@0.9.5) (2025-01-03)

**Note:** Version bump only for package @vtj/designer





## [0.9.4](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.3...@vtj/designer@0.9.4) (2025-01-03)


### Bug Fixes

* 🐛 文本框回车触发提交问题 ([b901a82](https://gitee.com/newgateway/vtj/commits/b901a82770e8f4ab52e20c71579137524faf7f8b))
* 🐛 style设置值为空时,出码异常 ([ee93c77](https://gitee.com/newgateway/vtj/commits/ee93c77d8473367b13c72d83d2f380e7f8a03bd0))





## [0.9.3](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.2...@vtj/designer@0.9.3) (2024-12-24)


### Features

* ✨ 数据源增加模拟数据类型 ([0a264d0](https://gitee.com/newgateway/vtj/commits/0a264d023c3a7c08ecad673e71fcd19e11a9e27d))





## [0.9.2](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.1...@vtj/designer@0.9.2) (2024-12-18)


### Features

* ✨ 支持自定义指令 ([8a193fd](https://gitee.com/newgateway/vtj/commits/8a193fddd211d4b69d282bdfbfe6e76c6352033f))
* ✨ v-html指令 ([189ffa7](https://gitee.com/newgateway/vtj/commits/189ffa76e8329d9a8d5c43346d1a1ff4d1377ea0))





## [0.9.1](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0...@vtj/designer@0.9.1) (2024-12-17)


### Features

* ✨ 支持定义登录页 ([c23e06f](https://gitee.com/newgateway/vtj/commits/c23e06faf7049e67525d5847925680404ff0bc76))





# [0.9.0](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.50...@vtj/designer@0.9.0) (2024-12-16)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.50](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.49...@vtj/designer@0.9.0-alpha.50) (2024-12-16)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.49](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.48...@vtj/designer@0.9.0-alpha.49) (2024-12-16)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.48](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.47...@vtj/designer@0.9.0-alpha.48) (2024-12-16)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.47](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.46...@vtj/designer@0.9.0-alpha.47) (2024-12-16)


### Bug Fixes

* 🐛 simulator style ([08af11b](https://gitee.com/newgateway/vtj/commits/08af11b3e4a7ba310eff971a3f5c871c5b501d18))





# [0.9.0-alpha.46](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.45...@vtj/designer@0.9.0-alpha.46) (2024-12-16)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.45](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.44...@vtj/designer@0.9.0-alpha.45) (2024-12-16)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.44](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.43...@vtj/designer@0.9.0-alpha.44) (2024-12-14)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.43](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.42...@vtj/designer@0.9.0-alpha.43) (2024-12-14)


### Bug Fixes

* 🐛 NodeEnv.Development ([a791d74](https://gitee.com/newgateway/vtj/commits/a791d74b6f2fcc57394bf501b75b7dd1aaedfc01))





# [0.9.0-alpha.42](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.41...@vtj/designer@0.9.0-alpha.42) (2024-12-11)


### Bug Fixes

* 🐛 html2canvas ([513fa07](https://gitee.com/newgateway/vtj/commits/513fa07e85404ca1e8daeb864a40dc039670c1f3))





# [0.9.0-alpha.41](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.40...@vtj/designer@0.9.0-alpha.41) (2024-12-09)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.40](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.39...@vtj/designer@0.9.0-alpha.40) (2024-12-06)


### Bug Fixes

* 🐛 ide avatar ([f7f1786](https://gitee.com/newgateway/vtj/commits/f7f17868a1e99f81645b123bf4371b71d7caeac1))





# [0.9.0-alpha.39](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.38...@vtj/designer@0.9.0-alpha.39) (2024-12-06)


### Bug Fixes

* 🐛 v-user-avatar ([547c9c1](https://gitee.com/newgateway/vtj/commits/547c9c12a85ea1bc9288e7ccef65eebdc78850d5))





# [0.9.0-alpha.38](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.37...@vtj/designer@0.9.0-alpha.38) (2024-12-06)


### Bug Fixes

* 🐛 auth ([aa40adc](https://gitee.com/newgateway/vtj/commits/aa40adce11037e75a82bfd1da60c7972972f34ae))





# [0.9.0-alpha.37](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.36...@vtj/designer@0.9.0-alpha.37) (2024-12-06)


### Bug Fixes

* 🐛 toRemoteAuth ([404a39f](https://gitee.com/newgateway/vtj/commits/404a39f6fa9a4841e57c218ccfe552abae973aa2))





# [0.9.0-alpha.36](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.35...@vtj/designer@0.9.0-alpha.36) (2024-12-06)


### Bug Fixes

* 🐛 toRemoteAuth ([a031815](https://gitee.com/newgateway/vtj/commits/a0318153269d9212e447b228f9aeb4734074af56))





# [0.9.0-alpha.35](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.34...@vtj/designer@0.9.0-alpha.35) (2024-12-06)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.34](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.33...@vtj/designer@0.9.0-alpha.34) (2024-12-06)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.33](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.32...@vtj/designer@0.9.0-alpha.33) (2024-12-06)


### Bug Fixes

* 🐛 onCoder ([c5c08da](https://gitee.com/newgateway/vtj/commits/c5c08da9c79487cdb5dffc9b9b430e59fc71a213))





# [0.9.0-alpha.32](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.31...@vtj/designer@0.9.0-alpha.32) (2024-12-05)


### Bug Fixes

* 🐛 toRemoteAuth ([a02d006](https://gitee.com/newgateway/vtj/commits/a02d0065bc0d23d72befb363118c1d71355b0303))





# [0.9.0-alpha.31](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.30...@vtj/designer@0.9.0-alpha.31) (2024-12-05)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.30](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.29...@vtj/designer@0.9.0-alpha.30) (2024-12-05)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.29](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.28...@vtj/designer@0.9.0-alpha.29) (2024-12-05)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.28](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.27...@vtj/designer@0.9.0-alpha.28) (2024-12-05)


### Bug Fixes

* 🐛 service.getFile ([4705065](https://gitee.com/newgateway/vtj/commits/4705065e3118fdb1342eded76feeaea884294f75))





# [0.9.0-alpha.27](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.26...@vtj/designer@0.9.0-alpha.27) (2024-12-05)


### Bug Fixes

* 🐛 request ([fc6edc3](https://gitee.com/newgateway/vtj/commits/fc6edc3a2f4585cf207b85cac81cd768a068767e))





# [0.9.0-alpha.26](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.25...@vtj/designer@0.9.0-alpha.26) (2024-12-05)


### Bug Fixes

* 🐛 删除节点,出码vue不更新 ([ef56f5b](https://gitee.com/newgateway/vtj/commits/ef56f5bbfed34b9a34f7faaac4533f24cd592b3f))





# [0.9.0-alpha.25](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.24...@vtj/designer@0.9.0-alpha.25) (2024-12-05)


### Bug Fixes

* 🐛 genSource ([dab2bce](https://gitee.com/newgateway/vtj/commits/dab2bce9244d61a2f2f416f1bf0efeb46884fbed))





# [0.9.0-alpha.24](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.23...@vtj/designer@0.9.0-alpha.24) (2024-12-04)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.23](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.22...@vtj/designer@0.9.0-alpha.23) (2024-12-04)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.22](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.21...@vtj/designer@0.9.0-alpha.22) (2024-12-04)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.21](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.20...@vtj/designer@0.9.0-alpha.21) (2024-12-04)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.20](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.19...@vtj/designer@0.9.0-alpha.20) (2024-12-04)


### Bug Fixes

* 🐛 designer ([e2d7a79](https://gitee.com/newgateway/vtj/commits/e2d7a79bb91050db5144d46db86a2c224af63368))





# [0.9.0-alpha.19](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.18...@vtj/designer@0.9.0-alpha.19) (2024-12-03)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.18](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.17...@vtj/designer@0.9.0-alpha.18) (2024-12-03)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.17](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.16...@vtj/designer@0.9.0-alpha.17) (2024-12-02)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.16](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.15...@vtj/designer@0.9.0-alpha.16) (2024-11-30)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.15](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.14...@vtj/designer@0.9.0-alpha.15) (2024-11-30)


### Bug Fixes

* 🐛 provider create route to chidlren ([7fce647](https://gitee.com/newgateway/vtj/commits/7fce6476a0e35a63986489a1d47378d6a759adae))





# [0.9.0-alpha.14](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.13...@vtj/designer@0.9.0-alpha.14) (2024-11-29)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.13](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.12...@vtj/designer@0.9.0-alpha.13) (2024-11-29)


### Bug Fixes

* 🐛 截图失败提示 ([b3e6eb4](https://gitee.com/newgateway/vtj/commits/b3e6eb481113cd9d0d2088bc6c8a332d53999ed6))


### Features

* ✨ engine srevice pass projectDsl param ([774cd9d](https://gitee.com/newgateway/vtj/commits/774cd9dba1de19f73293c264cc7c769759c66e98))





# [0.9.0-alpha.12](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.11...@vtj/designer@0.9.0-alpha.12) (2024-11-28)


### Features

* ✨ 支持删除模版 ([64bec04](https://gitee.com/newgateway/vtj/commits/64bec04a6300acaf3bc6d457f32240b32a8b895d))





# [0.9.0-alpha.11](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.10...@vtj/designer@0.9.0-alpha.11) (2024-11-28)


### Features

* ✨ mask 支持 menuBasePath ([5613bd7](https://gitee.com/newgateway/vtj/commits/5613bd793a873d5ce493e8184335ab3b4f845be8))





# [0.9.0-alpha.10](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.9...@vtj/designer@0.9.0-alpha.10) (2024-11-28)


### Features

* ✨ 用户信息 ([e6b237d](https://gitee.com/newgateway/vtj/commits/e6b237dcb7f561597b43b9f3ef96d9cbc728bba0))





# [0.9.0-alpha.9](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.8...@vtj/designer@0.9.0-alpha.9) (2024-11-27)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.8](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.7...@vtj/designer@0.9.0-alpha.8) (2024-11-22)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.7](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.6...@vtj/designer@0.9.0-alpha.7) (2024-11-22)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.6](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.5...@vtj/designer@0.9.0-alpha.6) (2024-11-22)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.5](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.4...@vtj/designer@0.9.0-alpha.5) (2024-11-20)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.4](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.3...@vtj/designer@0.9.0-alpha.4) (2024-11-20)


### Features

* ✨ 页面支持另存为区块 ([213435d](https://gitee.com/newgateway/vtj/commits/213435de3986fb996367aa8ff128e6f747caaaab))





# [0.9.0-alpha.3](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.2...@vtj/designer@0.9.0-alpha.3) (2024-11-19)


### Bug Fixes

* 🐛 monaco-editor ([f45bf5c](https://gitee.com/newgateway/vtj/commits/f45bf5c88f55ad21c68ae32a42b9e12225e342bb))





# [0.9.0-alpha.2](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.1...@vtj/designer@0.9.0-alpha.2) (2024-11-18)

**Note:** Version bump only for package @vtj/designer





# [0.9.0-alpha.1](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.9.0-alpha.0...@vtj/designer@0.9.0-alpha.1) (2024-11-18)


### Bug Fixes

* 🐛 link error ([faef175](https://gitee.com/newgateway/vtj/commits/faef175bd44b52fdf4d84644e2dce0ad70be232d))





# [0.9.0-alpha.0](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.172...@vtj/designer@0.9.0-alpha.0) (2024-11-18)


### Bug Fixes

* 🐛 设计器样式 ([82d7f45](https://gitee.com/newgateway/vtj/commits/82d7f4588344f86b23a3c55ec16bf3632052b128))
* 🐛 设计器app实例重复问题 ([8878ada](https://gitee.com/newgateway/vtj/commits/8878ada4cfa1b96541a8f3311f0983c66747088f))
* 🐛 修复模板和区块查询无结果展示 ([2f33dac](https://gitee.com/newgateway/vtj/commits/2f33daccd3d1a9024d1b4023196e621eba30fc95))
* 🐛 注释物料市场 ([6c93b67](https://gitee.com/newgateway/vtj/commits/6c93b67678a25dc3aa445aabbb3304c703176b48))
* 🐛 docs 刷新无效 ([39d6534](https://gitee.com/newgateway/vtj/commits/39d653474361464a611038031ccbd63e9de75efb))


### Features

* ✨ 模板发布功能 ([c9a05b7](https://gitee.com/newgateway/vtj/commits/c9a05b786717e57aca0769fbfd6e2da389527fe8))
* ✨ 模板管理 ([16bc3dc](https://gitee.com/newgateway/vtj/commits/16bc3dcefc7c96898769c182a4183614cf797daa))
* ✨ 模板模块 ([5b4217c](https://gitee.com/newgateway/vtj/commits/5b4217c70a7c31868afaf86cabc0dfcfe4d00dc5))
* ✨ 切换文件打开设计视图 ([89d1f98](https://gitee.com/newgateway/vtj/commits/89d1f98a48f6478889ceac31547df67e7c794557))
* ✨ 设计器支持纯净页面和路由元信息设置 ([3a11d0f](https://gitee.com/newgateway/vtj/commits/3a11d0fa1e1a19e7286e82f562165c07a5cf0aaa))
* ✨ 物料市场 ([7c009c0](https://gitee.com/newgateway/vtj/commits/7c009c0191ade61a47a76a7b27b61902c7ce6e55))
* ✨ item  box 操作按钮改为下拉菜单 ([8a13c33](https://gitee.com/newgateway/vtj/commits/8a13c33969f3c275a96e7ae0756028e6fbd3169e))
* ✨ scss modern-compiler ([ca6734f](https://gitee.com/newgateway/vtj/commits/ca6734f8af7072e79b2b7f6fafef6156593b75ab))





## [0.8.172](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.171...@vtj/designer@0.8.172) (2024-10-25)

**Note:** Version bump only for package @vtj/designer





## [0.8.171](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.170...@vtj/designer@0.8.171) (2024-10-24)

**Note:** Version bump only for package @vtj/designer





## [0.8.170](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.169...@vtj/designer@0.8.170) (2024-10-24)

**Note:** Version bump only for package @vtj/designer





## [0.8.169](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.168...@vtj/designer@0.8.169) (2024-10-24)

**Note:** Version bump only for package @vtj/designer





## [0.8.168](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.167...@vtj/designer@0.8.168) (2024-10-22)


### Bug Fixes

* 🐛 access adapter ([4ed5e17](https://gitee.com/newgateway/vtj/commits/4ed5e17d8746639598819457fe06c541c307d1a2))





## [0.8.167](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.166...@vtj/designer@0.8.167) (2024-10-22)

**Note:** Version bump only for package @vtj/designer





## [0.8.166](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.165...@vtj/designer@0.8.166) (2024-10-22)


### Features

* ✨ 设计器支持Access配置 ([4d1e91e](https://gitee.com/newgateway/vtj/commits/4d1e91ebcf1b00ad05ac11d1f05fbc96d1ffd06d))





## [0.8.165](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.164...@vtj/designer@0.8.165) (2024-10-21)

**Note:** Version bump only for package @vtj/designer





## [0.8.164](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.163...@vtj/designer@0.8.164) (2024-10-21)


### Bug Fixes

* 🐛 apis tag style ([0a7c12e](https://gitee.com/newgateway/vtj/commits/0a7c12e781d6d7ef6370d03c549a23812a99a4d0))






## [0.8.163](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.162...@vtj/designer@0.8.163) (2024-10-17)


### Bug Fixes

* 🐛 element-plus 2.8.5 bug 自定义校验函数导致ElSelect异常 ([79c2d71](https://gitee.com/newgateway/vtj/commits/79c2d71b3363afd4445a3a6f6738f1c998509a3a))





## [0.8.162](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.161...@vtj/designer@0.8.162) (2024-10-15)

**Note:** Version bump only for package @vtj/designer





## [0.8.161](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.160...@vtj/designer@0.8.161) (2024-10-14)

**Note:** Version bump only for package @vtj/designer






## [0.8.160](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.159...@vtj/designer@0.8.160) (2024-10-14)


### Bug Fixes

* 🐛 parseExpression error ([3c3f555](https://gitee.com/newgateway/vtj/commits/3c3f5559153ad8eba11ae688c84133421f2e9021))






## [0.8.159](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.158...@vtj/designer@0.8.159) (2024-10-11)

**Note:** Version bump only for package @vtj/designer






## [0.8.158](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.157...@vtj/designer@0.8.158) (2024-10-11)


### Features

* ✨ 区块管理支持分组 ([08f64a0](https://gitee.com/newgateway/vtj/commits/08f64a09088ef42142fc607ba8099afe6946c35f))
* ✨ api分组 ([bd029b0](https://gitee.com/newgateway/vtj/commits/bd029b0418ef48afac4830ed158e00c1771a55e0))





## [0.8.157](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.156...@vtj/designer@0.8.157) (2024-10-09)


### Bug Fixes

* 🐛 只有一个名为default的插槽，并且无参数，可以省略 ([3a16e2d](https://gitee.com/newgateway/vtj/commits/3a16e2dbdb776b44dbb6ca24c1ac57170503d5b7))





## [0.8.156](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.155...@vtj/designer@0.8.156) (2024-10-08)


### Bug Fixes

* 🐛 XDialog classList bug ([d96b8ba](https://gitee.com/newgateway/vtj/commits/d96b8ba4901f69249707a955638a40902d79bd73))






## [0.8.155](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.154...@vtj/designer@0.8.155) (2024-10-07)

**Note:** Version bump only for package @vtj/designer






## [0.8.154](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.153...@vtj/designer@0.8.154) (2024-10-07)

**Note:** Version bump only for package @vtj/designer





## [0.8.153](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.152...@vtj/designer@0.8.153) (2024-10-07)

**Note:** Version bump only for package @vtj/designer





## [0.8.152](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.151...@vtj/designer@0.8.152) (2024-10-04)

**Note:** Version bump only for package @vtj/designer





## [0.8.151](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.150...@vtj/designer@0.8.151) (2024-10-04)


### Bug Fixes

* 🐛 页面管理数量统计 ([0cdc62b](https://gitee.com/newgateway/vtj/commits/0cdc62bc2ab7a503b15b0c9a7745542a0dfe0fe7))






## [0.8.150](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.149...@vtj/designer@0.8.150) (2024-09-30)

**Note:** Version bump only for package @vtj/designer





## [0.8.149](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.148...@vtj/designer@0.8.149) (2024-09-27)

**Note:** Version bump only for package @vtj/designer





## [0.8.148](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.147...@vtj/designer@0.8.148) (2024-09-27)

**Note:** Version bump only for package @vtj/designer






## [0.8.147](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.146...@vtj/designer@0.8.147) (2024-09-25)


### Features

* ✨ 初始化vant物料 ([9279d61](https://gitee.com/newgateway/vtj/commits/9279d61ebbf90a2824a157ad2d182294627a3bad))






## [0.8.146](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.145...@vtj/designer@0.8.146) (2024-09-20)


### Bug Fixes

* 🐛 画布拖拽结束状态没更新 ([e5209d3](https://gitee.com/newgateway/vtj/commits/e5209d3fd010c7275f731816e0078a12a807fe9a))






## [0.8.145](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.144...@vtj/designer@0.8.145) (2024-09-20)


### Features

* ✨ 画布支持直接拖拽节点 ([5dde38d](https://gitee.com/newgateway/vtj/commits/5dde38d401aaa2e040e97e320fb09b67a3ed1b48))






## [0.8.144](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.143...@vtj/designer@0.8.144) (2024-09-19)

**Note:** Version bump only for package @vtj/designer





## [0.8.143](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.142...@vtj/designer@0.8.143) (2024-09-19)

**Note:** Version bump only for package @vtj/designer





## [0.8.142](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.141...@vtj/designer@0.8.142) (2024-09-18)


### Bug Fixes

* 🐛 设计器预览样式丢失问题 ([fb07321](https://gitee.com/newgateway/vtj/commits/fb0732140d4e095daf52b1cfa12a72e1ab65c178))





## [0.8.141](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.140...@vtj/designer@0.8.141) (2024-09-18)


### Features

* ✨ 打包移除vxe-table ([27e9fd5](https://gitee.com/newgateway/vtj/commits/27e9fd501d76d2890e656f9676e07150bbd1f72f))






## [0.8.140](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.139...@vtj/designer@0.8.140) (2024-09-18)


### Bug Fixes

* 🐛 dev-tools ([07008d9](https://gitee.com/newgateway/vtj/commits/07008d98d1778081ef0c9ed74f659babf0476fc6))





## [0.8.139](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.138...@vtj/designer@0.8.139) (2024-09-16)

**Note:** Version bump only for package @vtj/designer





## [0.8.138](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.137...@vtj/designer@0.8.138) (2024-09-16)

**Note:** Version bump only for package @vtj/designer





## [0.8.137](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.136...@vtj/designer@0.8.137) (2024-09-16)


### Bug Fixes

* 🐛 升级vue3.5 ([3b12449](https://gitee.com/newgateway/vtj/commits/3b12449447692487882539d43210d57dcc97a48a))
* 🐛 devtools ([7ec8411](https://gitee.com/newgateway/vtj/commits/7ec8411956b43a9eadd022c253f382d13233f596))






## [0.8.136](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.135...@vtj/designer@0.8.136) (2024-09-14)


### Bug Fixes

* 🐛 设计器样式污染预览页面样式 ([f3631cd](https://gitee.com/newgateway/vtj/commits/f3631cdb440c6f1d5a327763a4d4695835cf3782))






## [0.8.135](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.134...@vtj/designer@0.8.135) (2024-09-13)

**Note:** Version bump only for package @vtj/designer





## [0.8.134](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.133...@vtj/designer@0.8.134) (2024-09-12)

**Note:** Version bump only for package @vtj/designer






## [0.8.133](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.132...@vtj/designer@0.8.133) (2024-09-12)

**Note:** Version bump only for package @vtj/designer





## [0.8.132](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.131...@vtj/designer@0.8.132) (2024-09-12)


### Bug Fixes

* 🐛 devtools delay load ([2c20df7](https://gitee.com/newgateway/vtj/commits/2c20df765cb28ac14c1fd1c9b4e8aa773e9628bd))





## [0.8.131](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.130...@vtj/designer@0.8.131) (2024-09-12)


### Features

* ✨ devtools ([1afcfcb](https://gitee.com/newgateway/vtj/commits/1afcfcb1389dd65c3c94dda3ee7d0472424e9067))
* ✨ devtools module ([18e2949](https://gitee.com/newgateway/vtj/commits/18e294909e0533119f2d6f7d9fa33d470f3a6abb))






## [0.8.130](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.129...@vtj/designer@0.8.130) (2024-09-10)

**Note:** Version bump only for package @vtj/designer





## [0.8.129](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.128...@vtj/designer@0.8.129) (2024-09-10)

**Note:** Version bump only for package @vtj/designer






## [0.8.128](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.127...@vtj/designer@0.8.128) (2024-09-09)

**Note:** Version bump only for package @vtj/designer






## [0.8.127](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.126...@vtj/designer@0.8.127) (2024-09-09)

**Note:** Version bump only for package @vtj/designer






## [0.8.126](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.125...@vtj/designer@0.8.126) (2024-09-09)

**Note:** Version bump only for package @vtj/designer






## [0.8.125](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.124...@vtj/designer@0.8.125) (2024-09-08)


### Bug Fixes

* 🐛 更新依赖导致的兼容问题 ([d96fe5d](https://gitee.com/newgateway/vtj/commits/d96fe5d457ab1dc35ace670dca062a3cd86894c2))





## [0.8.124](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.123...@vtj/designer@0.8.124) (2024-09-06)


### Bug Fixes

* 🐛 plugin is undefined error ([82f370c](https://gitee.com/newgateway/vtj/commits/82f370cc3d8d1db757cffcd843454aa1b4e2653c))






## [0.8.123](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.122...@vtj/designer@0.8.123) (2024-09-02)

**Note:** Version bump only for package @vtj/designer





## [0.8.122](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.121...@vtj/designer@0.8.122) (2024-09-02)


### Bug Fixes

* 🐛 增加库依赖失效 ([441f71c](https://gitee.com/newgateway/vtj/commits/441f71cfbc6afcce41c83b00c881fc20ea287b42))






## [0.8.121](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.120...@vtj/designer@0.8.121) (2024-09-02)


### Bug Fixes

* 🐛 文件管理上传文件后url不刷新，调整pad视图尺寸 ([e9de789](https://gitee.com/newgateway/vtj/commits/e9de7894213108e4c2b84f9a8614e1ad62f247eb))


### Features

* ✨ 删除页面同时删除源码文件 ([7a67f51](https://gitee.com/newgateway/vtj/commits/7a67f51983118c8f982b6c18d1464c602d39943f))






## [0.8.120](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.119...@vtj/designer@0.8.120) (2024-08-31)


### Features

* ✨ 页面路由复制 ([0da2890](https://gitee.com/newgateway/vtj/commits/0da289059c7330ef67e49b329044bd828217edb0))






## [0.8.119](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.118...@vtj/designer@0.8.119) (2024-08-29)

**Note:** Version bump only for package @vtj/designer





## [0.8.118](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.117...@vtj/designer@0.8.118) (2024-08-29)

**Note:** Version bump only for package @vtj/designer






## [0.8.117](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.116...@vtj/designer@0.8.117) (2024-08-29)

**Note:** Version bump only for package @vtj/designer





## [0.8.116](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.115...@vtj/designer@0.8.116) (2024-08-28)


### Bug Fixes

* 🐛 setter labelWidth ([4b970f6](https://gitee.com/newgateway/vtj/commits/4b970f671577fe32ff60b80d2a65b67d51cae88b))





## [0.8.115](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.114...@vtj/designer@0.8.115) (2024-08-28)

**Note:** Version bump only for package @vtj/designer






## [0.8.114](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.113...@vtj/designer@0.8.114) (2024-08-27)

**Note:** Version bump only for package @vtj/designer






## [0.8.113](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.112...@vtj/designer@0.8.113) (2024-08-23)


### Features

* ✨ 自动判断label宽度 ([6b28fd4](https://gitee.com/newgateway/vtj/commits/6b28fd46f751da3d4d0f70d3efa457a3bd25a3cb))





## [0.8.112](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.111...@vtj/designer@0.8.112) (2024-08-22)

**Note:** Version bump only for package @vtj/designer





## [0.8.111](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.110...@vtj/designer@0.8.111) (2024-08-22)

**Note:** Version bump only for package @vtj/designer





## [0.8.110](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.109...@vtj/designer@0.8.110) (2024-08-22)

**Note:** Version bump only for package @vtj/designer






## [0.8.109](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.108...@vtj/designer@0.8.109) (2024-08-20)

**Note:** Version bump only for package @vtj/designer






## [0.8.108](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.107...@vtj/designer@0.8.108) (2024-08-18)


### Features

* ✨ 优化属性设置面板 ([d427a0f](https://gitee.com/newgateway/vtj/commits/d427a0fa3f848547d295256440847e394221f312))





## [0.8.107](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.106...@vtj/designer@0.8.107) (2024-08-17)

**Note:** Version bump only for package @vtj/designer





## [0.8.106](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.105...@vtj/designer@0.8.106) (2024-08-17)


### Bug Fixes

* 🐛 不校验上下文对象 ([ed79d7d](https://gitee.com/newgateway/vtj/commits/ed79d7d70ecff6070fa9e2a9d12b1818fe16d8e9))





## [0.8.105](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.104...@vtj/designer@0.8.105) (2024-08-16)

**Note:** Version bump only for package @vtj/designer





## [0.8.104](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.103...@vtj/designer@0.8.104) (2024-08-16)

**Note:** Version bump only for package @vtj/designer






## [0.8.103](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.102...@vtj/designer@0.8.103) (2024-08-14)

**Note:** Version bump only for package @vtj/designer





## [0.8.102](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.101...@vtj/designer@0.8.102) (2024-08-13)

**Note:** Version bump only for package @vtj/designer





## [0.8.101](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.100...@vtj/designer@0.8.101) (2024-08-13)

**Note:** Version bump only for package @vtj/designer






## [0.8.100](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.99...@vtj/designer@0.8.100) (2024-08-09)


### Bug Fixes

* 🐛 style设置border回填不正确 ([d05da3c](https://gitee.com/newgateway/vtj/commits/d05da3c28d6ec611a99b5417ba2db23bca72555d))





## [0.8.99](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.98...@vtj/designer@0.8.99) (2024-08-08)


### Bug Fixes

* 🐛 插槽构造错误 ([da1bafe](https://gitee.com/newgateway/vtj/commits/da1bafe2a0c6e2761dc954ac6647f22f2b13ef0e))





## [0.8.98](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.97...@vtj/designer@0.8.98) (2024-08-08)


### Bug Fixes

* 🐛 绑定上下文代码校验问题 ([01b3aca](https://gitee.com/newgateway/vtj/commits/01b3acaa69fe8ef8af30587ceaf6481a91a3221d))






## [0.8.97](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.96...@vtj/designer@0.8.97) (2024-07-31)

**Note:** Version bump only for package @vtj/designer






## [0.8.96](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.95...@vtj/designer@0.8.96) (2024-07-25)

**Note:** Version bump only for package @vtj/designer





## [0.8.95](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.94...@vtj/designer@0.8.95) (2024-07-22)

**Note:** Version bump only for package @vtj/designer





## [0.8.94](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.93...@vtj/designer@0.8.94) (2024-07-20)


### Bug Fixes

* 🐛 绑定器支持最大化 ([7c852f7](https://gitee.com/newgateway/vtj/commits/7c852f7514629d247ea498d0ac3170871dd6b551))





## [0.8.93](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.92...@vtj/designer@0.8.93) (2024-07-20)

**Note:** Version bump only for package @vtj/designer





## [0.8.92](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.91...@vtj/designer@0.8.92) (2024-07-19)

**Note:** Version bump only for package @vtj/designer





## [0.8.91](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.90...@vtj/designer@0.8.91) (2024-07-19)


### Bug Fixes

* 🐛 双向绑定属性提取错误 ([7482192](https://gitee.com/newgateway/vtj/commits/74821920edfc8495b2f4f388c07630f9906a35cd))





## [0.8.90](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.89...@vtj/designer@0.8.90) (2024-07-18)

**Note:** Version bump only for package @vtj/designer





## [0.8.89](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.88...@vtj/designer@0.8.89) (2024-07-18)

**Note:** Version bump only for package @vtj/designer





## [0.8.88](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.87...@vtj/designer@0.8.88) (2024-07-16)

**Note:** Version bump only for package @vtj/designer





## [0.8.87](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.86...@vtj/designer@0.8.87) (2024-07-16)

**Note:** Version bump only for package @vtj/designer





## [0.8.86](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.85...@vtj/designer@0.8.86) (2024-07-15)

**Note:** Version bump only for package @vtj/designer





## [0.8.85](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.84...@vtj/designer@0.8.85) (2024-07-15)

**Note:** Version bump only for package @vtj/designer





## [0.8.84](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.83...@vtj/designer@0.8.84) (2024-07-15)

**Note:** Version bump only for package @vtj/designer





## [0.8.83](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.82...@vtj/designer@0.8.83) (2024-07-12)

**Note:** Version bump only for package @vtj/designer





## [0.8.82](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.81...@vtj/designer@0.8.82) (2024-07-12)


### Bug Fixes

* 🐛 getVueInstance 异步组件失效 ([3774776](https://gitee.com/newgateway/vtj/commits/377477630d83a9e2b97aee95682e35bb6062ee4c))





## [0.8.81](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.80...@vtj/designer@0.8.81) (2024-07-12)

**Note:** Version bump only for package @vtj/designer





## [0.8.80](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.79...@vtj/designer@0.8.80) (2024-07-10)

**Note:** Version bump only for package @vtj/designer





## [0.8.79](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.78...@vtj/designer@0.8.79) (2024-07-10)


### Features

* ✨ 设计器支持动态插槽 ([c9f8d7e](https://gitee.com/newgateway/vtj/commits/c9f8d7e4016b6e410ff878a75f36513c8db45a19))





## [0.8.78](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.77...@vtj/designer@0.8.78) (2024-07-09)

**Note:** Version bump only for package @vtj/designer





## [0.8.77](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.76...@vtj/designer@0.8.77) (2024-07-09)


### Bug Fixes

* 🐛 接触绑定变量值改为undefined ([e49b4b4](https://gitee.com/newgateway/vtj/commits/e49b4b40f99e87f24bdc84ff96dc0cff36f84f8d))





## [0.8.76](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.75...@vtj/designer@0.8.76) (2024-07-08)

**Note:** Version bump only for package @vtj/designer





## [0.8.75](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.74...@vtj/designer@0.8.75) (2024-07-08)

**Note:** Version bump only for package @vtj/designer





## [0.8.74](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.73...@vtj/designer@0.8.74) (2024-07-06)

**Note:** Version bump only for package @vtj/designer





## [0.8.73](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.72...@vtj/designer@0.8.73) (2024-07-06)

**Note:** Version bump only for package @vtj/designer





## [0.8.72](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.71...@vtj/designer@0.8.72) (2024-07-06)

**Note:** Version bump only for package @vtj/designer





## [0.8.71](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.70...@vtj/designer@0.8.71) (2024-07-05)


### Performance Improvements

* ⚡ 优化错误提示 ([d965bda](https://gitee.com/newgateway/vtj/commits/d965bda468cda1d561a9f53c3a8e2f78ae24c4ad))





## [0.8.70](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.69...@vtj/designer@0.8.70) (2024-07-05)

**Note:** Version bump only for package @vtj/designer





## [0.8.69](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.68...@vtj/designer@0.8.69) (2024-07-04)

**Note:** Version bump only for package @vtj/designer





## [0.8.68](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.67...@vtj/designer@0.8.68) (2024-07-04)

**Note:** Version bump only for package @vtj/designer





## [0.8.67](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.66...@vtj/designer@0.8.67) (2024-07-03)

**Note:** Version bump only for package @vtj/designer





## [0.8.66](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.65...@vtj/designer@0.8.66) (2024-07-03)

**Note:** Version bump only for package @vtj/designer





## [0.8.65](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.64...@vtj/designer@0.8.65) (2024-07-02)

**Note:** Version bump only for package @vtj/designer





## [0.8.64](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.63...@vtj/designer@0.8.64) (2024-06-28)

**Note:** Version bump only for package @vtj/designer





## [0.8.63](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.62...@vtj/designer@0.8.63) (2024-06-28)


### Bug Fixes

* 🐛 修复设计器不能选中异步组件 ([f09c73e](https://gitee.com/newgateway/vtj/commits/f09c73e9b73e7577b98f8604537720d8f0d56122))





## [0.8.62](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.61...@vtj/designer@0.8.62) (2024-06-27)

**Note:** Version bump only for package @vtj/designer





## [0.8.61](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.60...@vtj/designer@0.8.61) (2024-06-26)

**Note:** Version bump only for package @vtj/designer





## [0.8.60](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.59...@vtj/designer@0.8.60) (2024-06-26)

**Note:** Version bump only for package @vtj/designer





## [0.8.59](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.58...@vtj/designer@0.8.59) (2024-06-26)

**Note:** Version bump only for package @vtj/designer





## [0.8.58](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.57...@vtj/designer@0.8.58) (2024-06-26)

**Note:** Version bump only for package @vtj/designer





## [0.8.57](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.56...@vtj/designer@0.8.57) (2024-06-25)

**Note:** Version bump only for package @vtj/designer





## [0.8.56](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.55...@vtj/designer@0.8.56) (2024-06-22)


### Features

* ✨ api管理支持搜索，帮助中心支持返回首页 ([cbc1e9c](https://gitee.com/newgateway/vtj/commits/cbc1e9c0eb2fe15e52b98491178c2ba5ed56f423))





## [0.8.55](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.54...@vtj/designer@0.8.55) (2024-06-21)

**Note:** Version bump only for package @vtj/designer





## [0.8.54](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.53...@vtj/designer@0.8.54) (2024-06-20)

**Note:** Version bump only for package @vtj/designer





## [0.8.53](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.52...@vtj/designer@0.8.53) (2024-06-20)

**Note:** Version bump only for package @vtj/designer





## [0.8.52](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.51...@vtj/designer@0.8.52) (2024-06-18)

**Note:** Version bump only for package @vtj/designer





## [0.8.51](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.50...@vtj/designer@0.8.51) (2024-06-16)

**Note:** Version bump only for package @vtj/designer





## [0.8.50](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.49...@vtj/designer@0.8.50) (2024-06-14)

**Note:** Version bump only for package @vtj/designer





## [0.8.49](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.48...@vtj/designer@0.8.49) (2024-06-14)


### Features

* ✨ 记录环境，在扩展时可能需要用到 ([18419e7](https://gitee.com/newgateway/vtj/commits/18419e74973dffe6010a2ac4c4b1bd04df5ec0b7))





## [0.8.48](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.47...@vtj/designer@0.8.48) (2024-06-14)

**Note:** Version bump only for package @vtj/designer





## [0.8.47](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.46...@vtj/designer@0.8.47) (2024-06-14)

**Note:** Version bump only for package @vtj/designer





## [0.8.46](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.45...@vtj/designer@0.8.46) (2024-06-14)

**Note:** Version bump only for package @vtj/designer





## [0.8.45](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.44...@vtj/designer@0.8.45) (2024-06-14)

**Note:** Version bump only for package @vtj/designer





## [0.8.44](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.43...@vtj/designer@0.8.44) (2024-06-13)

**Note:** Version bump only for package @vtj/designer





## [0.8.43](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.42...@vtj/designer@0.8.43) (2024-06-12)

**Note:** Version bump only for package @vtj/designer





## [0.8.42](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.41...@vtj/designer@0.8.42) (2024-06-12)

**Note:** Version bump only for package @vtj/designer





## [0.8.41](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.40...@vtj/designer@0.8.41) (2024-06-12)

**Note:** Version bump only for package @vtj/designer





## [0.8.40](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.39...@vtj/designer@0.8.40) (2024-06-06)


### Bug Fixes

* 🐛 修复类型、style透明度回填错误 ([eb92ad6](https://gitee.com/newgateway/vtj/commits/eb92ad66cff7e80197f09e91c21eff7dac2ea14f))





## [0.8.39](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.38...@vtj/designer@0.8.39) (2024-05-30)


### Features

* ✨ 事件插槽定义支持配置参数 ([d6728b3](https://gitee.com/newgateway/vtj/commits/d6728b330815a937cb582f0d0afb667f54bc590e))





## [0.8.38](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.37...@vtj/designer@0.8.38) (2024-05-29)

**Note:** Version bump only for package @vtj/designer





## [0.8.37](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.36...@vtj/designer@0.8.37) (2024-05-28)

**Note:** Version bump only for package @vtj/designer





## [0.8.36](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.35...@vtj/designer@0.8.36) (2024-05-27)

**Note:** Version bump only for package @vtj/designer





## [0.8.35](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.34...@vtj/designer@0.8.35) (2024-05-27)

**Note:** Version bump only for package @vtj/designer





## [0.8.34](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.33...@vtj/designer@0.8.34) (2024-05-24)

**Note:** Version bump only for package @vtj/designer





## [0.8.33](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.32...@vtj/designer@0.8.33) (2024-05-23)

**Note:** Version bump only for package @vtj/designer





## [0.8.32](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.30...@vtj/designer@0.8.32) (2024-05-23)


### Bug Fixes

* 🐛 provider install ([587112d](https://gitee.com/newgateway/vtj/commits/587112d873cb5738691be63b269d16e04ae9312e))


### Features

* ✨ ui物料 ([3429074](https://gitee.com/newgateway/vtj/commits/34290740f2a2f125c033b7e3cf3bcbea4e48c1bc))





## [0.8.31](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.30...@vtj/designer@0.8.31) (2024-05-14)

**Note:** Version bump only for package @vtj/designer





## [0.8.30](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.29...@vtj/designer@0.8.30) (2024-05-11)


### Features

* ✨ 变量绑定高级列表支持方法和数据类型展示 ([029d70b](https://gitee.com/newgateway/vtj/commits/029d70bbf5a7cb0e3fc376ee7b1283f4d4517a26))
* ✨ 变量绑定高级列表支持方法和数据类型展示 ([2d8bd17](https://gitee.com/newgateway/vtj/commits/2d8bd175226f6dee0bfa9f319d9c16ec876d265f))





## [0.8.29](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.28...@vtj/designer@0.8.29) (2024-05-11)


### Bug Fixes

* 🐛 provider install ([92f7535](https://gitee.com/newgateway/vtj/commits/92f75352286ec4956ce0b8b6cee752fab6730216))





## [0.8.28](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.27...@vtj/designer@0.8.28) (2024-05-09)


### Bug Fixes

* 🐛 更改帮助中心链接 ([e59ba08](https://gitee.com/newgateway/vtj/commits/e59ba0890a419a7b11bfd63e2120c2486abfcc3b))





## [0.8.27](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.26...@vtj/designer@0.8.27) (2024-05-08)


### Features

* ✨ 支持metaQuery ([94c2879](https://gitee.com/newgateway/vtj/commits/94c287930d3ae9bafab7419673b4ec5c4fc5c73a))





## [0.8.26](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.25...@vtj/designer@0.8.26) (2024-05-07)

**Note:** Version bump only for package @vtj/designer





## [0.8.25](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.24...@vtj/designer@0.8.25) (2024-05-07)

**Note:** Version bump only for package @vtj/designer





## [0.8.24](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.23...@vtj/designer@0.8.24) (2024-05-07)

**Note:** Version bump only for package @vtj/designer





## [0.8.23](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.22...@vtj/designer@0.8.23) (2024-05-07)

**Note:** Version bump only for package @vtj/designer





## [0.8.22](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.21...@vtj/designer@0.8.22) (2024-05-07)

**Note:** Version bump only for package @vtj/designer





## [0.8.21](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.20...@vtj/designer@0.8.21) (2024-05-06)

**Note:** Version bump only for package @vtj/designer





## [0.8.20](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.19...@vtj/designer@0.8.20) (2024-05-06)

**Note:** Version bump only for package @vtj/designer





## [0.8.19](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.18...@vtj/designer@0.8.19) (2024-05-06)

**Note:** Version bump only for package @vtj/designer





## [0.8.18](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.17...@vtj/designer@0.8.18) (2024-05-04)

**Note:** Version bump only for package @vtj/designer





## [0.8.17](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.16...@vtj/designer@0.8.17) (2024-05-02)


### Features

* ✨ extension add params ([ce517b8](https://gitee.com/newgateway/vtj/commits/ce517b8075a491186fc69ae8a757d810022538d4))





## [0.8.16](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.15...@vtj/designer@0.8.16) (2024-05-02)

**Note:** Version bump only for package @vtj/designer





## [0.8.15](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.14...@vtj/designer@0.8.15) (2024-05-02)


### Bug Fixes

* 🐛 engine 处理 adapter ([03478a9](https://gitee.com/newgateway/vtj/commits/03478a9ce700dddcaccc5f5922f4e3391ad001b2))





## [0.8.14](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.13...@vtj/designer@0.8.14) (2024-05-02)

**Note:** Version bump only for package @vtj/designer





## [0.8.13](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.12...@vtj/designer@0.8.13) (2024-05-02)


### Features

* ✨ 插件增加install参数 ([45b704b](https://gitee.com/newgateway/vtj/commits/45b704b445bf7f7c935844a2ee53b5714f4f2140))





## [0.8.12](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.11...@vtj/designer@0.8.12) (2024-05-01)

**Note:** Version bump only for package @vtj/designer





## [0.8.11](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.10...@vtj/designer@0.8.11) (2024-05-01)

**Note:** Version bump only for package @vtj/designer





## [0.8.10](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.9...@vtj/designer@0.8.10) (2024-05-01)

**Note:** Version bump only for package @vtj/designer





## [0.8.9](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.8...@vtj/designer@0.8.9) (2024-04-30)


### Features

* ✨ 设计器支持远程扩展 ([ed2ed8e](https://gitee.com/newgateway/vtj/commits/ed2ed8ec38f51d389d0eb05488a3e8e06a1fdd35))






## [0.8.8](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.7...@vtj/designer@0.8.8) (2024-04-27)


### Bug Fixes

* 🐛 deps ([f88b4f7](https://gitee.com/newgateway/vtj/commits/f88b4f72a634e48fa516e2b1e8e0c87d3a968ef2))





## [0.8.7](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.5...@vtj/designer@0.8.7) (2024-04-26)


### Features

* ✨ add charts module ([2e5b7e9](https://gitee.com/newgateway/vtj/commits/2e5b7e9ca763a2446d3e65af6fa8d1d32b8f2243))





## [0.8.6](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.5...@vtj/designer@0.8.6) (2024-04-26)


### Features

* ✨ add charts module ([2e5b7e9](https://gitee.com/newgateway/vtj/commits/2e5b7e9ca763a2446d3e65af6fa8d1d32b8f2243))






## [0.8.5](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.4...@vtj/designer@0.8.5) (2024-04-24)


### Bug Fixes

* 🐛 更新项目信息不能正常渲染插件 ([a8ddf43](https://gitee.com/newgateway/vtj/commits/a8ddf4356dc03e1b430ebeba4195c1af88c20827))
* 🐛 加载插件css文件环境错误 ([43dface](https://gitee.com/newgateway/vtj/commits/43dface3f7d0c5c3ed78a8586567f06392be7040))





## [0.8.4](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.3...@vtj/designer@0.8.4) (2024-04-24)


### Bug Fixes

* 🐛 设计画布拖拽组件需要判断，不能有无限递归 ([6f0d412](https://gitee.com/newgateway/vtj/commits/6f0d412fe45cfc9428ea88c6b84cf4074177ac5c))






## [0.8.3](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.2...@vtj/designer@0.8.3) (2024-04-23)


### Bug Fixes

* 🐛 大纲树中拖拽子节点放置到根节点，dsl没有更新 ([c6a020b](https://gitee.com/newgateway/vtj/commits/c6a020b8b5f5fdec9b5e58a87fb750589f2d5668))
* 🐛 历史记录需要回写block dsl ([5002912](https://gitee.com/newgateway/vtj/commits/500291222323f57ee5846a382a63821c51a7d8b1))


### Features

* ✨ 设计画布支持拖拽 ([a4e8866](https://gitee.com/newgateway/vtj/commits/a4e8866a4e0bf118fcfca37cd98aa81bb1029059))






## [0.8.2](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.1...@vtj/designer@0.8.2) (2024-04-22)


### Bug Fixes

* 🐛 编译后不应该调service.init; 出码$props 改为 props ([b3ab003](https://gitee.com/newgateway/vtj/commits/b3ab003c59df81225da8b0a43593f2b28f7bf53b))
* 🐛 出码作用域插槽没传递出来 ([bfdcf63](https://gitee.com/newgateway/vtj/commits/bfdcf630d5a67d4e7d6e60cb23537da9a89ae224))
* 🐛 定义事件的事件名称不支持 update:modelValue 格式，无法增加双向绑定 ([3cba3c7](https://gitee.com/newgateway/vtj/commits/3cba3c76687f2e3a0842ae6957f69266c0fd92fe))





## [0.8.1](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.8.0...@vtj/designer@0.8.1) (2024-04-22)


### Bug Fixes

* 🐛 cli template ([911c3a0](https://gitee.com/newgateway/vtj/commits/911c3a0e2bb60548affe5dcf5a496577809d63b8))






# [0.8.0](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.7.34...@vtj/designer@0.8.0) (2024-04-22)


### Bug Fixes

* 🐛 修复组件渲染根节点是text类型时无法选中设置属性 ([4bce89e](https://gitee.com/newgateway/vtj/commits/4bce89e21608d1eabeb89de4f451ee2abb556feb))


### Features

* ✨ 依赖支持语言包设置 ([362190e](https://gitee.com/newgateway/vtj/commits/362190e66d663412d2d07261bc29b9ef439af8ed))
* ✨ 支持 UrlSchema ([edae2c5](https://gitee.com/newgateway/vtj/commits/edae2c52ce88ad72a4a7c31844385dc083249e72))
* ✨ 支持区块插件 ([88b5028](https://gitee.com/newgateway/vtj/commits/88b5028cdb142dd9f5642c51ecb9f978333858ce))
* ✨ add FileSetter ([8467349](https://gitee.com/newgateway/vtj/commits/8467349162e8694808fd3fdcd2354d8bb6de086f))


### Performance Improvements

* ⚡ 代码优化 ([8d5e2d3](https://gitee.com/newgateway/vtj/commits/8d5e2d366876cd1c91eb8e4c7b30237e65dd33b5))






## [0.7.34](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.7.33...@vtj/designer@0.7.34) (2024-04-10)


### Bug Fixes

* 🐛 designer about ([0c17f56](https://gitee.com/newgateway/vtj/commits/0c17f56644bbf9f3af974b838917ae5d82881bcd))






## [0.7.33](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.7.32...@vtj/designer@0.7.33) (2024-04-08)


### Bug Fixes

* 🐛 ant-design-vue reset.css path error ([add106d](https://gitee.com/newgateway/vtj/commits/add106dfb13ddb57b3247bbd2683e862c48aecd1))
* 🐛 deps merge ([8c5670b](https://gitee.com/newgateway/vtj/commits/8c5670b2caa4a2c594a7af0e25c0ece9fafb16e6))


### Features

* ✨ 设计器支持ckeditor ([5225869](https://gitee.com/newgateway/vtj/commits/522586927c1ef13ff7e1c02b5df962092805f4a0))






## [0.7.32](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.7.31...@vtj/designer@0.7.32) (2024-04-03)


### Bug Fixes

* 🐛 大纲树default插槽不显示 ([67e7266](https://gitee.com/newgateway/vtj/commits/67e7266c1e92073b95262a1b69caf038a0d00692))






## [0.7.31](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.7.30...@vtj/designer@0.7.31) (2024-04-03)


### chore

* 🚀 格式化提交信息 ([fede392](https://gitee.com/newgateway/vtj/commits/fede3924392a8297d2b2fe37565fd975116b8bf2))


### Features

* ✨ api mock ([df7400f](https://gitee.com/newgateway/vtj/commits/df7400f1c2f7aa20f24e5217b177a38877de5cdd))


### BREAKING CHANGES

* 🧨 no






## [0.7.31](https://gitee.com/newgateway/vtj/compare/@vtj/designer@0.7.30...@vtj/designer@0.7.31) (2024-04-03)


### chore

* 🚀 格式化提交信息 ([fede392](https://gitee.com/newgateway/vtj/commits/fede3924392a8297d2b2fe37565fd975116b8bf2))


### Features

* ✨ api mock ([df7400f](https://gitee.com/newgateway/vtj/commits/df7400f1c2f7aa20f24e5217b177a38877de5cdd))


### BREAKING CHANGES

* 🧨 no
