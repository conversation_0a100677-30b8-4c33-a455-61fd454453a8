@use '../core' as *;

@include b(pages-widget) {
  .el-tree {
    --el-tree-node-hover-bg-color: transparent;
    .v-item {
      margin: 0;
    }
  }
  .el-tree-node__content {
    height: 28px;
  }
  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    background-color: transparent;
  }
  .v-item__subtitle {
    font-size: 12px;
  }

  .is-drop-inner .is-drop-inner {
    background: var(--el-color-warning-light-8);
    .v-item {
      opacity: 0.6;
    }
  }

  @include e(item) {
    &.is-layout {
      background-color: var(--el-fill-color) !important;
      &.is-active {
        background-color: var(--el-color-primary-light-9) !important;
      }
    }
  }
}

@include b(pages-widget-form) {
  .x-field__editor_wrap {
    height: 100%;
  }
}
