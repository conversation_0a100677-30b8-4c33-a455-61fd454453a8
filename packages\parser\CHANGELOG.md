# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [0.12.70](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.69...@vtj/parser@0.12.70) (2025-08-01)

**Note:** Version bump only for package @vtj/parser





## [0.12.69](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.68...@vtj/parser@0.12.69) (2025-08-01)

**Note:** Version bump only for package @vtj/parser





## [0.12.68](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.67...@vtj/parser@0.12.68) (2025-07-31)

**Note:** Version bump only for package @vtj/parser





## [0.12.67](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.66...@vtj/parser@0.12.67) (2025-07-31)


### Bug Fixes

* 🐛 parser TEXT_CALL ([f3fa726](https://gitee.com/newgateway/vtj/commits/f3fa726ce59a2a8b96da93e4415dd4b50dfb9bca))





## [0.12.66](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.65...@vtj/parser@0.12.66) (2025-07-26)


### Bug Fixes

* 🐛 parser libs methods ([8fc0640](https://gitee.com/newgateway/vtj/commits/8fc06409905f0af3801cd63cf2b2046adf566637))
* 🐛 parser replacer ([f1f15e3](https://gitee.com/newgateway/vtj/commits/f1f15e346009485d9294bb87b69dca4e711aa375))





## [0.12.65](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.64...@vtj/parser@0.12.65) (2025-07-23)


### Bug Fixes

* 🐛 coder双引号问题 ([74ff98f](https://gitee.com/newgateway/vtj/commits/74ff98fc80bd4091b9b42b4454bb3469e6eee235))





## [0.12.64](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.63...@vtj/parser@0.12.64) (2025-07-23)


### Bug Fixes

* 🐛 出码插槽支持scope名称 ([ca896df](https://gitee.com/newgateway/vtj/commits/ca896df0515e0f7b1904e5c839b81010b4c45355))





## [0.12.63](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.62...@vtj/parser@0.12.63) (2025-07-19)

**Note:** Version bump only for package @vtj/parser





## [0.12.62](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.61...@vtj/parser@0.12.62) (2025-07-19)


### Bug Fixes

* 🐛 coder 双引号问题 ([bec5a28](https://gitee.com/newgateway/vtj/commits/bec5a28bf8404cebc73ce6747c090a175d40eea3))





## [0.12.61](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.60...@vtj/parser@0.12.61) (2025-07-19)


### Bug Fixes

* 🐛 parser 节点id重新生成问题 ([a4416f8](https://gitee.com/newgateway/vtj/commits/a4416f8092d728812e56bd67d7aa509d89fec8d7))
* 🐛 parser 删除 @babel/core 依赖 ([76cefe2](https://gitee.com/newgateway/vtj/commits/76cefe2b95abfcce7584f81b2e66282b94f0cb3c))
* 🐛 parser 数据源方法重复 ([a47cabe](https://gitee.com/newgateway/vtj/commits/a47cabef1fb4efd0b00cd4cacb532a6f0fd638e7))





## [0.12.60](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.59...@vtj/parser@0.12.60) (2025-07-18)

**Note:** Version bump only for package @vtj/parser





## [0.12.59](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.58...@vtj/parser@0.12.59) (2025-07-18)

**Note:** Version bump only for package @vtj/parser





## [0.12.58](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.57...@vtj/parser@0.12.58) (2025-07-16)


### Bug Fixes

* 🐛 parser 解决重复图标问题 ([56a7a1f](https://gitee.com/newgateway/vtj/commits/56a7a1f7098ad003fc1558d8d28fc34122ed50bc))





## [0.12.57](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.56...@vtj/parser@0.12.57) (2025-07-16)


### Bug Fixes

* 🐛 parser script ([b2f03a1](https://gitee.com/newgateway/vtj/commits/b2f03a1a4c9470f77c3a9aa719d16ffddfe6b657))





## [0.12.56](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.55...@vtj/parser@0.12.56) (2025-07-16)


### Bug Fixes

* 🐛 parser 数据源 ([9befb29](https://gitee.com/newgateway/vtj/commits/9befb2968ecc74f33b01202317e966c8f3bbadcd))
* 🐛 parser watch ([9404146](https://gitee.com/newgateway/vtj/commits/940414637c3c079014f3d1c0f5dfcd53edf2f693))


### Features

* ✨ 代码校验并自动修复 ([205257b](https://gitee.com/newgateway/vtj/commits/205257b75b5ebcb5c02ec4ade0000388552277a6))
* ✨ parser validator and fixer ([0a7e29c](https://gitee.com/newgateway/vtj/commits/0a7e29cee2d89fce800521c43af989252d937ea3))
* ✨ parser watch ([26a942a](https://gitee.com/newgateway/vtj/commits/26a942a1b1297fa7d5c7ad49121b9175efce5b62))





## [0.12.55](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.54...@vtj/parser@0.12.55) (2025-07-09)

**Note:** Version bump only for package @vtj/parser





## [0.12.54](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.53...@vtj/parser@0.12.54) (2025-07-08)


### Bug Fixes

* 🐛 升级依赖 ([dbee35b](https://gitee.com/newgateway/vtj/commits/dbee35bd867a44f8c71c117fa90d56d108144a6b))





## [0.12.53](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.52...@vtj/parser@0.12.53) (2025-07-07)

**Note:** Version bump only for package @vtj/parser





## [0.12.52](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.51...@vtj/parser@0.12.52) (2025-07-07)


### Bug Fixes

* 🐛 0.12.51 ([e4d8797](https://gitee.com/newgateway/vtj/commits/e4d8797a041f6df63b7f9bba3a984f6992db1064))





## [0.12.51](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.50...@vtj/parser@0.12.51) (2025-07-07)


### Bug Fixes

* 🐛 parseStyle 禁止添加charset ([ceb1a6c](https://gitee.com/newgateway/vtj/commits/ceb1a6cf63027506840efc81d5c730ee57c934b2))





## [0.12.50](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.49...@vtj/parser@0.12.50) (2025-07-04)

**Note:** Version bump only for package @vtj/parser





## [0.12.49](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.48...@vtj/parser@0.12.49) (2025-07-02)

**Note:** Version bump only for package @vtj/parser





## [0.12.48](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.47...@vtj/parser@0.12.48) (2025-06-30)

**Note:** Version bump only for package @vtj/parser





## [0.12.47](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.46...@vtj/parser@0.12.47) (2025-06-24)

**Note:** Version bump only for package @vtj/parser





## [0.12.46](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.45...@vtj/parser@0.12.46) (2025-06-23)

**Note:** Version bump only for package @vtj/parser





## [0.12.45](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.44...@vtj/parser@0.12.45) (2025-06-23)

**Note:** Version bump only for package @vtj/parser





## [0.12.44](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.43...@vtj/parser@0.12.44) (2025-06-20)


### Bug Fixes

* 🐛 openApi ([d29d618](https://gitee.com/newgateway/vtj/commits/d29d618dc071f21ac3042b6b0ba305ac68fffc55))





## [0.12.43](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.42...@vtj/parser@0.12.43) (2025-06-17)

**Note:** Version bump only for package @vtj/parser





## [0.12.42](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.41...@vtj/parser@0.12.42) (2025-06-17)


### Bug Fixes

* 🐛 parser template v-if v-for 文本节点或多个元素节点 ([b154964](https://gitee.com/newgateway/vtj/commits/b154964eb6e8403b2a0c2c0d9bda3605ebccd435))





## [0.12.41](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.40...@vtj/parser@0.12.41) (2025-06-16)

**Note:** Version bump only for package @vtj/parser





## [0.12.40](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.39...@vtj/parser@0.12.40) (2025-06-12)

**Note:** Version bump only for package @vtj/parser





## [0.12.39](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.38...@vtj/parser@0.12.39) (2025-06-11)

**Note:** Version bump only for package @vtj/parser





## [0.12.38](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.37...@vtj/parser@0.12.38) (2025-06-11)

**Note:** Version bump only for package @vtj/parser





## [0.12.37](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.36...@vtj/parser@0.12.37) (2025-06-09)

**Note:** Version bump only for package @vtj/parser





## [0.12.36](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.35...@vtj/parser@0.12.36) (2025-06-09)


### Bug Fixes

* 🐛 compileScopedCSS ([84689ea](https://gitee.com/newgateway/vtj/commits/84689eae582dd0a97fc17dff1516bbc01e94bae1))
* 🐛 parser css 丢失媒体查询 ([378acab](https://gitee.com/newgateway/vtj/commits/378acab6283390bdb826beda3471c819ca7b900d))





## [0.12.35](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.34...@vtj/parser@0.12.35) (2025-06-06)

**Note:** Version bump only for package @vtj/parser





## [0.12.34](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.33...@vtj/parser@0.12.34) (2025-06-06)


### Bug Fixes

* 🐛 parser ([f7ce7ea](https://gitee.com/newgateway/vtj/commits/f7ce7eaabe571c25b7af232061e0c1fd81346c54))





## [0.12.33](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.32...@vtj/parser@0.12.33) (2025-06-04)

**Note:** Version bump only for package @vtj/parser





## [0.12.32](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.31...@vtj/parser@0.12.32) (2025-06-03)


### Bug Fixes

* 🐛 parser svg tag ([7abc984](https://gitee.com/newgateway/vtj/commits/7abc98461abbe2bc39e4d5d84068444ec86f6a6e))





## [0.12.31](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.30...@vtj/parser@0.12.31) (2025-05-29)

**Note:** Version bump only for package @vtj/parser





## [0.12.30](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.29...@vtj/parser@0.12.30) (2025-05-27)


### Bug Fixes

* 🐛 更换登录地址 ([ccac53e](https://gitee.com/newgateway/vtj/commits/ccac53ed6367543992322afcd90c90130695949d))





## [0.12.29](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.28...@vtj/parser@0.12.29) (2025-05-23)

**Note:** Version bump only for package @vtj/parser





## [0.12.28](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.27...@vtj/parser@0.12.28) (2025-05-23)

**Note:** Version bump only for package @vtj/parser





## [0.12.27](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.26...@vtj/parser@0.12.27) (2025-05-23)


### Bug Fixes

* 🐛 parser支持解析scss ([328b430](https://gitee.com/newgateway/vtj/commits/328b43008280ceb4ca4bfc5d5857bd804ac61278))





## [0.12.26](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.25...@vtj/parser@0.12.26) (2025-05-23)


### Bug Fixes

* 🐛 ai dsl error ([5b1ae66](https://gitee.com/newgateway/vtj/commits/5b1ae66f0ba5215bf677e0a3cc4fdeac3101c6a3))





## [0.12.25](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.24...@vtj/parser@0.12.25) (2025-05-22)


### Bug Fixes

* 🐛 提示词优化 ([eb6c0cb](https://gitee.com/newgateway/vtj/commits/eb6c0cbbe792dec515ee0e309b6dfc48795e4764))





## [0.12.24](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.23...@vtj/parser@0.12.24) (2025-05-21)


### Bug Fixes

* 🐛 提示词 ([b0f8dba](https://gitee.com/newgateway/vtj/commits/b0f8dba2c6d13c40e7f442c8b4eb38887b2bfab3))
* 🐛 提示词优化 ([7314cdd](https://gitee.com/newgateway/vtj/commits/7314cdd91fc45613f9958030ac4fd54fc9ac654e))
* 🐛 修复提示词 ([1d8960e](https://gitee.com/newgateway/vtj/commits/1d8960e702c9bb298636968ff2a8cc1ee8f63be2))
* 🐛 优化提示词 ([97345f3](https://gitee.com/newgateway/vtj/commits/97345f361271ba58bd8b402daf96fc3250c56c16))
* 🐛 charts导出错误 ([25b9d7c](https://gitee.com/newgateway/vtj/commits/25b9d7c99828e7eb20961f69ac06ad38b55318d8))





## [0.12.23](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.22...@vtj/parser@0.12.23) (2025-05-21)


### Bug Fixes

* 🐛 优化提示词 ([04472da](https://gitee.com/newgateway/vtj/commits/04472dacc78aea5606ab62c795d359ca2edb2ddc))





## [0.12.22](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.21...@vtj/parser@0.12.22) (2025-05-20)

**Note:** Version bump only for package @vtj/parser





## [0.12.21](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.20...@vtj/parser@0.12.21) (2025-05-16)


### Bug Fixes

* 🐛 parser ([c2eb712](https://gitee.com/newgateway/vtj/commits/c2eb71202a084b79bfd9a6797ba901cc80a068e2))
* 🐛 parser ([146cb3c](https://gitee.com/newgateway/vtj/commits/146cb3c2258715af338e97547129c1defd3bf882))





## [0.12.20](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.19...@vtj/parser@0.12.20) (2025-05-15)

**Note:** Version bump only for package @vtj/parser





## [0.12.19](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.18...@vtj/parser@0.12.19) (2025-05-12)


### Bug Fixes

* 🐛 parser block schema miss ([596df07](https://gitee.com/newgateway/vtj/commits/596df07e4fdb42d35cdbde5b9aeeef564ce12bdb))





## [0.12.18](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.17...@vtj/parser@0.12.18) (2025-05-12)


### Bug Fixes

* 🐛 parser utils ([03abd89](https://gitee.com/newgateway/vtj/commits/03abd89054d2daa55345024669a859f8585f8980))





## [0.12.17](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.16...@vtj/parser@0.12.17) (2025-05-12)

**Note:** Version bump only for package @vtj/parser





## [0.12.16](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.15...@vtj/parser@0.12.16) (2025-05-12)


### Bug Fixes

* 🐛 优化提示词 ([52b467c](https://gitee.com/newgateway/vtj/commits/52b467c1bc978fb189ddc318c8e019e6da7e1a37))
* 🐛 优化提示词 ([0b466f0](https://gitee.com/newgateway/vtj/commits/0b466f0f02d84784d65f29cb1f3763df1ce3c307))
* 🐛 parser commit 分支异常 ([f80359d](https://gitee.com/newgateway/vtj/commits/f80359d4fbb1d2216c1832b0f30f516202ee1d2a))





## [0.12.15](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.14...@vtj/parser@0.12.15) (2025-05-07)


### Bug Fixes

* 🐛 优化提示词 ([f893c9d](https://gitee.com/newgateway/vtj/commits/f893c9df00f591a0a54a47c09e72b2fad0fa74e9))
* 🐛 html解析class错误问题 ([f60e663](https://gitee.com/newgateway/vtj/commits/f60e663f075a7560be8d0126813a9bab9ac44eb2))
* 🐛 parser  uniapp html tag error ([05769ce](https://gitee.com/newgateway/vtj/commits/05769ceb92d401cef667bd64778a6b767303e328))





## [0.12.14](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.13...@vtj/parser@0.12.14) (2025-05-07)

**Note:** Version bump only for package @vtj/parser





## [0.12.13](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.12...@vtj/parser@0.12.13) (2025-05-06)


### Bug Fixes

* 🐛 icons import miss ([a610c53](https://gitee.com/newgateway/vtj/commits/a610c53c5944dc3dd1b3ef556001e4c70a36f5a3))





## [0.12.12](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.11...@vtj/parser@0.12.12) (2025-05-06)


### Features

* ✨ 支持图标组件物料 ([62a22fd](https://gitee.com/newgateway/vtj/commits/62a22fd139a70107668bac46a025797855e36703))





## [0.12.11](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.10...@vtj/parser@0.12.11) (2025-05-05)


### Features

* ✨ 源码视图支持编辑 ([8d794df](https://gitee.com/newgateway/vtj/commits/8d794df79f76e919a566d55310ff533c63a844b3))
* ✨ 支持v-else-if 和 v-else 代码分支 ([6965137](https://gitee.com/newgateway/vtj/commits/69651374b83bffd4bb65f4e8d7de82eb4dc87e7d))





## [0.12.10](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.9...@vtj/parser@0.12.10) (2025-04-30)


### Bug Fixes

* 🐛 检查AI是否有省略代码 ([3852e4b](https://gitee.com/newgateway/vtj/commits/3852e4b3725593423a99179804fb84573f1bbe69))
* 🐛 parser class merge ([4bd398c](https://gitee.com/newgateway/vtj/commits/4bd398c62c2c70cb72fe408b8e3eff29e8f4f180))





## [0.12.9](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.8...@vtj/parser@0.12.9) (2025-04-29)


### Bug Fixes

* 🐛 不校验scss ([4f7f427](https://gitee.com/newgateway/vtj/commits/4f7f42773bfdae2d41457417bcdc0e068adbd82a))
* 🐛 优化提示词 ([f226a42](https://gitee.com/newgateway/vtj/commits/f226a42d0d45ab749a8fa4923819b5b6ebe05baa))





## [0.12.8](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.7...@vtj/parser@0.12.8) (2025-04-29)


### Bug Fixes

* 🐛 parser html style bug ([d8dd06f](https://gitee.com/newgateway/vtj/commits/d8dd06f0e682ce3d358b8a1130f19f936c3d2cba))


### Features

* ✨ AI 图生代码 ([dd7fee5](https://gitee.com/newgateway/vtj/commits/dd7fee5c281ed9fce05cad14ae8ec2c37b9f0b0c))





## [0.12.7](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.6...@vtj/parser@0.12.7) (2025-04-27)


### Bug Fixes

* 🐛 uniapp coder ([3b88493](https://gitee.com/newgateway/vtj/commits/3b8849388d6c24bdde10ce7d82615275063dae64))





## [0.12.6](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.5...@vtj/parser@0.12.6) (2025-04-25)


### Bug Fixes

* 🐛 文本和表达式合成 ([6452a85](https://gitee.com/newgateway/vtj/commits/6452a859baa6e65c6ca8de40f93d432f2e7fa7c8))





## [0.12.5](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.4...@vtj/parser@0.12.5) (2025-04-25)


### Bug Fixes

* 🐛 设计器接入指南 ([bd08152](https://gitee.com/newgateway/vtj/commits/bd081521d049176dfc334305a7829780363b0d81))
* 🐛 修复html解析片段丢失问题 ([7899df2](https://gitee.com/newgateway/vtj/commits/7899df2747b37f28c761c52224d6a18fd0992e34))
* 🐛 优化提示词 ([e948bea](https://gitee.com/newgateway/vtj/commits/e948bea7488a823e5baaabc40f35a722db8245fc))
* 🐛 parser computed error ([964f8a7](https://gitee.com/newgateway/vtj/commits/964f8a7184c5298652878022fa98ded6a68cae85))





## [0.12.4](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.3...@vtj/parser@0.12.4) (2025-04-23)


### Bug Fixes

* 🐛 修复静态html丢失问题 ([42a087e](https://gitee.com/newgateway/vtj/commits/42a087e08efea3f683a46961b31d60c5f80c0903))





## [0.12.3](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.2...@vtj/parser@0.12.3) (2025-04-23)


### Bug Fixes

* 🐛 修复parser已知问题 ([c86b738](https://gitee.com/newgateway/vtj/commits/c86b738a2ed95eb02c1f4070c4b2a57c247d36e1))





## [0.12.2](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.1...@vtj/parser@0.12.2) (2025-04-22)

**Note:** Version bump only for package @vtj/parser





## [0.12.1](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.0...@vtj/parser@0.12.1) (2025-04-22)

**Note:** Version bump only for package @vtj/parser





# [0.12.0](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.0-alpha.2...@vtj/parser@0.12.0) (2025-04-22)

**Note:** Version bump only for package @vtj/parser





# [0.12.0-alpha.2](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.0-alpha.1...@vtj/parser@0.12.0-alpha.2) (2025-04-22)

**Note:** Version bump only for package @vtj/parser





# [0.12.0-alpha.1](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.12.0-alpha.0...@vtj/parser@0.12.0-alpha.1) (2025-04-22)

**Note:** Version bump only for package @vtj/parser





# [0.12.0-alpha.0](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.16...@vtj/parser@0.12.0-alpha.0) (2025-04-21)


### Bug Fixes

* 🐛 统一 [@babel](https://gitee.com/babel) 依赖版本 ([bdf967a](https://gitee.com/newgateway/vtj/commits/bdf967a061562deb95898163c113017abae015f4))
* 🐛 parseVue ([1f40a4c](https://gitee.com/newgateway/vtj/commits/1f40a4cce6f9849126b417611c59438a06ab47a2))
* 🐛 replacer ([2811a20](https://gitee.com/newgateway/vtj/commits/2811a20110e14129fe29722cca15e9e5ea16cf08))
* 🐛 transformChildren ([0fa144e](https://gitee.com/newgateway/vtj/commits/0fa144e104e1218f757e7cbed63a07f0dd69e11d))


### Features

* ✨ 节点/事件解析 ([27670e2](https://gitee.com/newgateway/vtj/commits/27670e2e7d52c1d76a2f278819b0d4beefa8e735))
* ✨ AI Widget ([e758a1b](https://gitee.com/newgateway/vtj/commits/e758a1b1e00496a20444d42c427288984a5766a5))
* ✨ AI助手 ([702d912](https://gitee.com/newgateway/vtj/commits/702d91255d2860ee899f06f598d8043e6db9620d))
* ✨ parser ([a8f8cad](https://gitee.com/newgateway/vtj/commits/a8f8cad6c156a670417537154ee3b5ad8745b349))
* ✨ parser style ([4e22bc8](https://gitee.com/newgateway/vtj/commits/4e22bc8aa2b94dea68b5e4b4c8ec32bb051b60dc))
* ✨ parser v-model ([ef2aa04](https://gitee.com/newgateway/vtj/commits/ef2aa04c050540609e7f23c15d2a9a2cfedb2dec))
* ✨ parseVue ([ad40b5b](https://gitee.com/newgateway/vtj/commits/ad40b5be0314eeae722f65915bcb8412815d4591))
* ✨ project add __UID__ ([b3315ce](https://gitee.com/newgateway/vtj/commits/b3315cee0c40de4f2591006a5ad56a2d1e92f318))





## [0.11.16](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.15...@vtj/parser@0.11.16) (2025-04-10)

**Note:** Version bump only for package @vtj/parser





## [0.11.15](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.14...@vtj/parser@0.11.15) (2025-04-09)

**Note:** Version bump only for package @vtj/parser





## [0.11.14](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.13...@vtj/parser@0.11.14) (2025-04-07)

**Note:** Version bump only for package @vtj/parser





## [0.11.13](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.12...@vtj/parser@0.11.13) (2025-04-02)

**Note:** Version bump only for package @vtj/parser





## [0.11.12](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.11...@vtj/parser@0.11.12) (2025-03-28)

**Note:** Version bump only for package @vtj/parser





## [0.11.11](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.10...@vtj/parser@0.11.11) (2025-03-28)

**Note:** Version bump only for package @vtj/parser





## [0.11.10](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.9...@vtj/parser@0.11.10) (2025-03-27)

**Note:** Version bump only for package @vtj/parser





## [0.11.9](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.8...@vtj/parser@0.11.9) (2025-03-27)

**Note:** Version bump only for package @vtj/parser





## [0.11.8](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.7...@vtj/parser@0.11.8) (2025-03-27)

**Note:** Version bump only for package @vtj/parser





## [0.11.7](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.6...@vtj/parser@0.11.7) (2025-03-25)

**Note:** Version bump only for package @vtj/parser





## [0.11.6](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.5...@vtj/parser@0.11.6) (2025-03-21)

**Note:** Version bump only for package @vtj/parser





## [0.11.5](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.4...@vtj/parser@0.11.5) (2025-03-20)

**Note:** Version bump only for package @vtj/parser





## [0.11.4](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.3...@vtj/parser@0.11.4) (2025-03-19)

**Note:** Version bump only for package @vtj/parser





## [0.11.3](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.2...@vtj/parser@0.11.3) (2025-03-19)

**Note:** Version bump only for package @vtj/parser





## [0.11.2](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.1...@vtj/parser@0.11.2) (2025-03-19)

**Note:** Version bump only for package @vtj/parser





## [0.11.1](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.1-alpha.1...@vtj/parser@0.11.1) (2025-03-18)

**Note:** Version bump only for package @vtj/parser





## [0.11.1-alpha.1](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.1-alpha.0...@vtj/parser@0.11.1-alpha.1) (2025-03-18)

**Note:** Version bump only for package @vtj/parser





## [0.11.1-alpha.0](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.11.0...@vtj/parser@0.11.1-alpha.0) (2025-03-18)

**Note:** Version bump only for package @vtj/parser





# [0.11.0](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.15...@vtj/parser@0.11.0) (2025-03-17)

**Note:** Version bump only for package @vtj/parser





## [0.10.15](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.14...@vtj/parser@0.10.15) (2025-03-14)

**Note:** Version bump only for package @vtj/parser





## [0.10.14](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.13...@vtj/parser@0.10.14) (2025-03-14)

**Note:** Version bump only for package @vtj/parser





## [0.10.13](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.12...@vtj/parser@0.10.13) (2025-03-13)

**Note:** Version bump only for package @vtj/parser





## [0.10.12](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.11...@vtj/parser@0.10.12) (2025-03-11)

**Note:** Version bump only for package @vtj/parser





## [0.10.11](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.10...@vtj/parser@0.10.11) (2025-03-11)

**Note:** Version bump only for package @vtj/parser





## [0.10.10](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.9...@vtj/parser@0.10.10) (2025-03-07)

**Note:** Version bump only for package @vtj/parser





## [0.10.9](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.8...@vtj/parser@0.10.9) (2025-03-04)

**Note:** Version bump only for package @vtj/parser





## [0.10.8](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.7...@vtj/parser@0.10.8) (2025-03-04)

**Note:** Version bump only for package @vtj/parser





## [0.10.7](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.5...@vtj/parser@0.10.7) (2025-03-04)

**Note:** Version bump only for package @vtj/parser





## [0.10.6](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.5...@vtj/parser@0.10.6) (2025-02-28)

**Note:** Version bump only for package @vtj/parser





## [0.10.5](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.4...@vtj/parser@0.10.5) (2025-02-27)

**Note:** Version bump only for package @vtj/parser





## [0.10.4](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.3...@vtj/parser@0.10.4) (2025-02-26)

**Note:** Version bump only for package @vtj/parser





## [0.10.3](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.2...@vtj/parser@0.10.3) (2025-02-26)

**Note:** Version bump only for package @vtj/parser





## [0.10.2](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.1...@vtj/parser@0.10.2) (2025-02-25)

**Note:** Version bump only for package @vtj/parser





## [0.10.1](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.1-alpha.7...@vtj/parser@0.10.1) (2025-02-25)

**Note:** Version bump only for package @vtj/parser





## [0.10.1-alpha.7](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.1-alpha.6...@vtj/parser@0.10.1-alpha.7) (2025-02-25)

**Note:** Version bump only for package @vtj/parser





## [0.10.1-alpha.6](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.1-alpha.5...@vtj/parser@0.10.1-alpha.6) (2025-02-24)

**Note:** Version bump only for package @vtj/parser





## [0.10.1-alpha.5](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.1-alpha.4...@vtj/parser@0.10.1-alpha.5) (2025-02-24)

**Note:** Version bump only for package @vtj/parser





## [0.10.1-alpha.4](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.1-alpha.3...@vtj/parser@0.10.1-alpha.4) (2025-02-24)

**Note:** Version bump only for package @vtj/parser





## [0.10.1-alpha.3](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.1-alpha.2...@vtj/parser@0.10.1-alpha.3) (2025-02-22)

**Note:** Version bump only for package @vtj/parser





## [0.10.1-alpha.2](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.1-alpha.1...@vtj/parser@0.10.1-alpha.2) (2025-02-22)

**Note:** Version bump only for package @vtj/parser





## [0.10.1-alpha.1](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.1-alpha.0...@vtj/parser@0.10.1-alpha.1) (2025-02-20)

**Note:** Version bump only for package @vtj/parser





## [0.10.1-alpha.0](https://gitee.com/newgateway/vtj/compare/@vtj/parser@0.10.0...@vtj/parser@0.10.1-alpha.0) (2025-02-19)

**Note:** Version bump only for package @vtj/parser





# 0.10.0 (2025-02-19)


### Bug Fixes

* 🐛 uniapp tabBar ([45a05d4](https://gitee.com/newgateway/vtj/commits/45a05d49d53645aaddb7a841b23b961e5337c3f9))


### Features

* ✨ @vtj/parser ([ebb0807](https://gitee.com/newgateway/vtj/commits/ebb08075377a7a0f960e2dabe1e014df37457d5d))
* ✨ uniapp 支持全局css ([18fb654](https://gitee.com/newgateway/vtj/commits/18fb654e13691b7226b77b6b93379b876d2089a2))
* ✨ uniapp coder ([7eaf0f3](https://gitee.com/newgateway/vtj/commits/7eaf0f3413b8cfbeb153e2d35b885975739a4945))
